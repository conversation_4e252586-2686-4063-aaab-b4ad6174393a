package com.cisp.identity.api.service.common;

import com.cisp.identity.api.annotation.BusiFuncBind;
import com.cisp.identity.api.constant.BusiFuncMeta;
import com.cisp.identity.api.constant.Constants;
import com.cisp.identity.api.model.vo.SsoVo;
import com.cisp.identity.api.model.vo.UserVo;
import com.seewo.kishframework.rest.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

@Tag(name = "/common/AuthenUserApiService", description = "用户认证服务")
@Path(Constants.API_PREFIX + "/common/authen")
@Consumes({MediaType.APPLICATION_JSON})
@Produces({MediaType.APPLICATION_JSON})
public interface AuthenCommonRestApi {



    @Operation(summary = "获取当前登录用户信息")
    @GET
    @Path("/current-user")
    @BusiFuncBind(BusiFuncMeta.COMMON_AUTHENTICATION_FUNC)
    RestResponse<UserVo> getCurrentUser(@HeaderParam("x-tenant-id") String tenantId);


    @Operation(summary = "获取当前登录用户token")
    @GET
    @Path("/token")
    @BusiFuncBind(BusiFuncMeta.COMMON_AUTHENTICATION_FUNC)
    RestResponse<String> getUserToken();


    /**
     * 单点登录设置token
     * @param ssoVo
     * @return
     */
    @Operation(summary = "设置单点登录token")
    @POST
    @Path("/set-token")
    Response setToken(SsoVo ssoVo);


    @Operation(summary = "清除单点登录token")
    @POST
    @Path("/clear-token")
    @BusiFuncBind(BusiFuncMeta.COMMON_AUTHENTICATION_FUNC)
    Response clearToken();

}
