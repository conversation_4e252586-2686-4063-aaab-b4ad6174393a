package com.cisp.identity.api.service.impl.common;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.cisp.identity.api.model.vo.SsoVo;
import com.cisp.identity.api.model.vo.UserVo;
import com.cisp.identity.api.service.common.AuthenCommonRestApi;
import com.cisp.identity.api.model.dto.UserDto;
import com.cisp.identity.application.model.dto.TenantDto;
import com.cisp.identity.application.service.TenantAppService;
import com.cisp.identity.application.service.UserAppService;
import com.cisp.identity.infrastructure.convert.UserMapper;
import com.seewo.kishframework.rest.RestResponse;
import com.seewo.kishframework.rest.VoidRestResponse;
import com.seewo.kishframework.security.SecurityContext;
import com.seewo.kishframework.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import javax.ws.rs.core.NewCookie;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@DubboService(protocol = "rest", register = false)
@Slf4j
public class AuthenCommonRestApiImpl implements AuthenCommonRestApi {
    @Autowired
    private UserAppService userAppService;
    @Autowired
    private UserMapper userMapper;

    @Autowired
    private TenantAppService tenantAppService;

    @Resource(name = "httpClient")
    private OkHttpClient httpClient;
    @Value("${kish.security.appCode}")
    private String appCode;
    @Value("${system.account.domian:https://account.test.seewo.com}")
    private String accountDomain;

    private static final String UC_SSO_GET_TOKEN_URL = "/api/v2/cas/exchangeToken";

    private static final String UC_SSO_CLEAR_TOKEN_URL = "/seewo-account/api/v2/auth";

    @Override
    public RestResponse<UserVo> getCurrentUser(String tenantId) {
        final SecurityContext context = SecurityUtils.context();
        context.checkPermission();
        String userId = context.getUserId();
        tenantId = StrUtil.isBlank(tenantId) ? getFirstTenant(userId).map(TenantDto::getTenantId).orElse(null) : tenantId;
        UserDto user = userAppService.getUser(tenantId,userId);
        return RestResponse.success(userMapper.toVo(user));
    }

    @Override
    public RestResponse<String> getUserToken() {
        final SecurityContext context = SecurityUtils.context();
        context.checkPermission();
        return RestResponse.success(context.getAuthToken());
    }

    @Override
    public javax.ws.rs.core.Response setToken(SsoVo ssoVo) {
        String token = StrUtil.isBlank(ssoVo.getToken())? getToken(ssoVo.getTicket(),ssoVo.getDomain()) : ssoVo.getToken();
        return javax.ws.rs.core.Response.ok().cookie(createCookie(token,3600 * 24 * 7))
                .entity(RestResponse.success(MapUtil.of("token",token)))
                .build();
    }

    @Override
    public javax.ws.rs.core.Response clearToken() {
        SecurityUtils.context().checkPermission();
        NewCookie cookie = createCookie("",0);
        // 构建 DELETE 请求，同时设置请求头
        Request request = new Request.Builder()
                .url(accountDomain + UC_SSO_CLEAR_TOKEN_URL)
                .delete() // 设置请求方法为 DELETE
                .addHeader("x-auth-app", appCode) // 设置请求头
                .addHeader("x-auth-token", SecurityUtils.context().getAuthToken()) // 示例：添加授权头
                .build();
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
        } catch (IOException e) {
            log.error("清除令牌时请求失败", e);
        }
        return javax.ws.rs.core.Response.ok(VoidRestResponse.success()).cookie(cookie).build();
    }

    private String getToken(String ticket,String domain){
        HttpUrl.Builder urlBuilder = HttpUrl.parse(domain+ UC_SSO_GET_TOKEN_URL).newBuilder();
        urlBuilder.addQueryParameter("ticket", ticket);
        String finalUrl = urlBuilder.build().toString();
        // 创建请求对象
        Request request = new Request.Builder().url(finalUrl).build();
        // 执行请求并获取响应
        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
                String token = response.body().string();
                Map<String,String> map = JSONUtil.toBean(token, Map.class);
                if(StrUtil.isNotEmpty(map.get("token"))){
                    return map.get("token");
                }
            } else {
                throw new IOException("Unexpected code " + response);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return "";
    }

    private NewCookie createCookie(String token,Integer expireTime) {
        return new NewCookie(
                "x-token",  // Cookie 名称
                token,    // Cookie 值
                "/",      // 路径
                ".kindlink.com.cn",     // 域名
                "Token for authentication", // 注释
                expireTime,     // 过期时间，单位：秒
                false,    // 是否为安全 Cookie
                true     // 是否为 HttpOnly Cookie
        );
    }

    private Optional<TenantDto> getFirstTenant(String userId){
        List<TenantDto> userTenants = tenantAppService.getUserTenants(userId);
        return CollectionUtil.isEmpty(userTenants) ? Optional.empty() : Optional.of(userTenants.get(0));
    }
}
