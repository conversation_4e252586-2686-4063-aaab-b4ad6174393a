package com.cisp.identity.infrastructure.config;

import com.cisp.identity.api.service.AuthenApiService;
import com.cisp.identity.api.service.AuthorApiService;
import com.cisp.identity.api.service.UserApiService;
import com.cisp.identity.application.service.IdentityAuthService;
import com.cisp.infrastructure.config.SystemConfiguration;
import com.seewo.kishframework.security.AuthService;
import com.seewo.kishframework.security.UserService;
import com.seewo.kishframework.spring.security.SecurityAutoConfiguration;
import com.seewo.kishframework.spring.security.SecurityProperties;
import com.seewo.kishframework.spring.security.UcConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
@EnableConfigurationProperties({SecurityProperties.class})
@AutoConfigureBefore({SecurityAutoConfiguration.class, UcConfiguration.class})
public class KishAutoConfiguration {
    @DubboReference
    private AuthenApiService authApiService;
    @DubboReference
    private AuthorApiService authorApiService;
    @DubboReference
    private UserApiService userApiService;

    @Autowired
    private SystemConfiguration systemConfiguration;

    @Bean
    @ConditionalOnMissingBean({UserService.class, AuthService.class})
    public IdentityAuthService identityAuthService() {
        return new IdentityAuthService(authApiService, authorApiService, userApiService, systemConfiguration);
    }
}
