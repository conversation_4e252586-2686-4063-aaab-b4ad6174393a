package com.cisp.identity.acl;
import com.cisp.identity.application.model.dto.PlatFormMenuDto;
import com.cisp.identity.api.model.dto.RoleDto;

import java.util.List;


public interface AuthorizationCenterAclService {
    boolean hasPermission(String userId, String permissionId, String groupId);

    List<String> getPermissionGroups(String userId, String permissionId, String groupType);


    List<RoleDto> getRoles(String tenantId,String userId);

    List<PlatFormMenuDto> getMenus(String tenantId,String userId);




}
