package com.cisp.identity.application.service;

import com.cisp.identity.api.model.dto.NodeDto;
import com.cisp.identity.api.model.dto.UserImageDto;
import com.cisp.identity.api.model.query.NodeQueryDto;
import com.cisp.identity.api.model.query.NodeQueryVo;
import com.cisp.identity.api.model.vo.StaffVo;
import com.cisp.identity.api.model.vo.UnitTreeVo;
import com.cisp.identity.api.model.vo.UserVo;

import java.util.List;

/**
 * 节点服务(获取班级/年级/学生/老师)
 */
public interface BasicInfoAppService {

    List<NodeDto> getNodes(NodeQueryDto nodeQueryDto);

    List<UserImageDto> getAllUserFaces(String tenantId);


    List<UnitTreeVo> getUnitTree(String tenantId);


    String getPhone(String userId);

    List<StaffVo> getStaffs(String tenantId);

    List<NodeDto> getUsersByNode(NodeQueryDto nodeQueryDto);

}
