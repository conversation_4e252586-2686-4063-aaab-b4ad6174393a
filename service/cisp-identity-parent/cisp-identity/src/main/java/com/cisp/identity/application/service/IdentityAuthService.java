package com.cisp.identity.application.service;

import com.cisp.identity.api.model.vo.AuthUserVo;
import com.cisp.identity.api.model.vo.UserVo;
import com.cisp.identity.api.service.AuthenApiService;
import com.cisp.identity.api.service.AuthorApiService;
import com.cisp.identity.api.service.UserApiService;
import com.cisp.infrastructure.common.context.ContextUtils;
import com.cisp.infrastructure.config.SystemConfiguration;
import com.seewo.kishframework.rest.BooleanRestResponse;
import com.seewo.kishframework.rest.RestResponse;
import com.seewo.kishframework.security.AuthService;
import com.seewo.kishframework.security.User;
import com.seewo.kishframework.security.UserService;

import java.util.List;

public class IdentityAuthService implements UserService, AuthService {

    private AuthenApiService authenApiService;

    private AuthorApiService authorApiService;

    private UserApiService userApiService;

    private SystemConfiguration systemConfiguration;

    public IdentityAuthService() {

    }

    public IdentityAuthService(AuthenApiService authenApiService, AuthorApiService authorApiService,
                               UserApiService userApiService, SystemConfiguration systemConfiguration) {
        this.authenApiService = authenApiService;
        this.authorApiService = authorApiService;
        this.userApiService = userApiService;
        this.systemConfiguration = systemConfiguration;
    }

    @Override
    public String getUserId(String authToken, String ip) {
        if (ContextUtils.isAuthorized()){
            return authToken;
        }
        final RestResponse<AuthUserVo> authUserVoRestResponse = authenApiService.validateAccessToken(authToken);
        return authUserVoRestResponse.unwrap().getUserId();
    }

    @Override
    public boolean hasPermission(String userId, String permissionId) {
        final BooleanRestResponse booleanRestResponse = authorApiService.hasPermission(userId, permissionId);

        return booleanRestResponse.unwrap();
    }

    @Override
    public boolean hasPermission(String userId, String permissionId, String groupId) {
        final BooleanRestResponse booleanRestResponse = authorApiService.hasPermission(userId, permissionId, groupId);

        return booleanRestResponse.unwrap();
    }

    @Override
    public List<String> getPermissionGroups(String userId, String permissionId, String groupType) {
        final RestResponse<List<String>> permissionGroups = authorApiService.getPermissionGroups(userId, permissionId, groupType);

        return permissionGroups.unwrap();
    }

    @Override
    public User getUser(String userId) {

        UserVo userResultDto = userApiService.getUser(userId).unwrap();
        if (userResultDto == null) {
            return null;
        }
        User user = new User();
        user.setUserId(userResultDto.getId());
        user.setAccount(userResultDto.getAccount());
        user.setNickName(userResultDto.getNickName());
        user.setRealName(userResultDto.getRealName());
        user.setPhone(userResultDto.getPhone());
        user.setEmail(userResultDto.getEmail());
        return user;
    }
}
