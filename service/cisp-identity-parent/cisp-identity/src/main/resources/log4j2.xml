<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
<Appenders>
    <Console name="Console" target="SYSTEM_OUT">
        <PatternLayout pattern="[%d{yyyy-MM-dd HH:mm:ss.SSS}][%X{trace_id}][%X{span_id}][%t][%level][%C:%L] %m%n"/>
    </Console>
    <RollingFile name="RollingFile" fileName="logs/app.log"
                 filePattern="logs/app-%d{yyyy-MM-dd}.log">
        <PatternLayout pattern="[%d{yyyy-MM-dd HH:mm:ss.SSS}][%X{trace_id}][%X{span_id}][%t][%level][%C:%L] %m%n" />
        <Policies>
            <TimeBasedTriggeringPolicy/>
            <SizeBasedTriggeringPolicy size="250MB"/>
        </Policies>
        <DefaultRolloverStrategy max="30"/>
    </RollingFile>
</Appenders>
    <Loggers>
        <Root level="INFO">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFile"/>
        </Root>
        <Logger name="org.hibernate" level="WARN"/>
        <Logger name="com.seewoedu.course.domain" level="WARN"/>
        <Logger name="com.seewo.sentinel" level="WARN"/>
        <!--<Logger name="com.zaxxer.hikari.pool.HikariPool" level="DEBUG"/>-->
    </Loggers>
</Configuration>