module.log=com.p6spy.engine.logging.P6LogFactory,com.p6spy.engine.outage.P6OutageFactory
# èªå®ä¹æ¥å¿æå°
logMessageFormat=com.p6spy.engine.spy.appender.CustomLineFormat
customLogMessageFormat=SQLèæ¶ï¼ %(executionTime) ms | è¿æ¥ä¿¡æ¯ï¼ %(category)-%(connectionId) | æ§è¡è¯­å¥ï¼ %(sql)
# ä½¿ç¨slf4jè®°å½sql
appender=com.p6spy.engine.spy.appender.Slf4JLogger
## éç½®è®°å½Logä¾å¤
excludecategories=info,debug,result,batc,resultset
# è®¾ç½®ä½¿ç¨p6spy driveræ¥åä»£ç
deregisterdrivers=true
# æ¥ææ ¼å¼
dateformat=yyyy-MM-dd HH:mm:ss
# å®éé©±å¨
driverlist=com.mysql.cj.jdbc.Driver
# æ¯å¦å¼å¯æ¢SQLè®°å½
outagedetection=true
# æ¢SQLè®°å½æ å ç§
outagedetectioninterval=0.5