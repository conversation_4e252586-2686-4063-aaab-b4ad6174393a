package com.cisp.device.api.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 录制标记对象
 */


@Data
public class RecordMarkVo {
    @Schema(description = "标记ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "标记ID不能为空")
    private String id;

    @Schema(description = "孪生体ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "孪生体ID不能为空")
    private BigInteger twinId;

    @Schema(description = "当前时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal currentTime;

    @Schema(description = "标记文件cstore key", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "标记文件cstore key不能为空")
    private String fileKey;

    @Schema(description = "标记文件cstore下载地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "标记文件cstore下载地址不能为空")
    private String fileUrl;

    private Long latency;

    @Schema(description = "标记时间点", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "标记时间点不能为空")
    private Long realTime;

    private String remarks;

}
