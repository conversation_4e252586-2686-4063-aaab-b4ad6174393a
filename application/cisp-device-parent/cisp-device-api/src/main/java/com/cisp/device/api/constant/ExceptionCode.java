package com.cisp.device.api.constant;

import com.seewo.kishframework.exception.BusinessException;
import com.seewo.kishframework.exception.CodeBaseException;
import com.seewo.kishframework.exception.PlatformExceptionCode;
import com.seewo.kishframework.exception.SystemException;
import com.seewo.kishframework.util.EnumWithValue;

/**
 * 异常枚举
 * 编码规则(6/5/4 xxxx xxx,前面一位用来区分业务异常和系统异常, 后面三位用来区分子系统)
 * BusinessExcepion： 6/4xxxx xxx , 6开头
 * SystemException:   5xxxx xxx , 5开头
 * 框架预定义的通用异常 com.seewo.kishframework.exception.PlatformExceptionCode
 */
public enum ExceptionCode implements EnumWithValue<String> {

    ;

    private final EnumWithValue delegate;

    ExceptionCode(EnumWithValue delegate) {
        this.delegate = delegate;
    }

    @Override
    public EnumWithValue<String> getDelegate() {
        return this.delegate;
    }

    public CodeBaseException exception() {
        return exception(this.getText());
    }

    public CodeBaseException exception(String message, Object... args) {
        if (this.getValue().startsWith("6") || this.getValue().startsWith("4")) {
            return new BusinessException(this.getValue(), isNoneBlank(message) ? String.format(message, args) : this.getText());
        } else if (this.getValue().startsWith("5")) {
            return new SystemException(this.getValue(), isNoneBlank(message) ? String.format(message, args) : this.getText());
        } else {
            throw PlatformExceptionCode.COMMON_SYSTEM_EXCEPTION.exception("ExceptionCode编码不符合规范");
        }
    }

    private boolean isNoneBlank(String message) {
        return message != null && !"".equals(message);
    }
}