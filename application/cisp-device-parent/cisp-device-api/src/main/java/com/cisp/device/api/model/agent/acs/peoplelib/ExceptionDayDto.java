package com.cisp.device.api.model.agent.acs.peoplelib;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * 异常日期 DTO
 * 用于表示特殊日期的时间安排
 */
@Data
public class ExceptionDayDto {
    
    /**
     * 异常日期ID
     */
    private Integer id;
    
    /**
     * 是否启用，0-禁用，1-启用
     */
    private Integer enabled;
    
    /**
     * 日期，格式为 yyyy-MM-dd
     */
    private String date;
    
    /**
     * 时间段信息列表
     */
    private List<TimeSectionInfoDto> timeSectionInfos;
}
