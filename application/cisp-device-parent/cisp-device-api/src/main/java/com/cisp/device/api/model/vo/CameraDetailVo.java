package com.cisp.device.api.model.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 摄像头详细信息
 */
@Data
public class CameraDetailVo {

    private String capability;

    private String deviceKey;

    private String devSerialNum;

    private String indexCode;

    private String ip;

    private String manufacturer;

    private String name;

    private String netZoneId;

    private String port;

    private String regionIndexCode;

    private String resourceType;

    private String treatyType;

    private String createTime;

    private String updateTime;

    private String dataVersion;

    private String description;

    private String deviceModel;

    private Integer isCascade;

    private Integer sort;

    private Integer disOrder;

    private String regionName;

    private String regionPath;

    private String regionPathName;

}
