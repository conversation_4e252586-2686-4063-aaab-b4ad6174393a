package com.cisp.device.api.model.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 */
@Data
public class ImportVo {

    /**
     * 学校编号ID
     */
    @Schema(description = "学校编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "学校编码不能为空")
    @Size(min = 4, max = 4, message = "学校编码长度为4")
    private String groupId;

}
