package com.cisp.device.api.service.impl;

import com.cisp.device.api.constant.Constants;
import com.cisp.device.api.service.SwaggerApiService;
import com.google.common.collect.Sets;
import io.swagger.v3.jaxrs2.integration.JaxrsOpenApiContextBuilder;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.integration.SwaggerConfiguration;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.Operation;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.parameters.Parameter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@DubboService(protocol = "rest", register = false)
@Service("device.SwaggerApiServiceImpl")
public class SwaggerApiServiceImpl implements SwaggerApiService {
    private static List<Parameter> paramters = new ArrayList<>();

    static {
        {
            Parameter parameter = new Parameter();
            parameter.setName("token");
            parameter.setDescription("认证token");
            parameter.setIn(ParameterIn.HEADER.toString());
            parameter.setRequired(Boolean.FALSE);
            paramters.add(parameter);
        }
        /*{
            Parameter parameter = new Parameter();
            parameter.setName("x-kish-tenant");
            parameter.setDescription("租户ID");
            parameter.setIn(ParameterIn.HEADER.toString());
            parameter.setRequired(Boolean.FALSE);
            paramters.add(parameter);
        }*/
    }

    @SneakyThrows
    @Override
    public Object listAll() {
        SwaggerConfiguration oasConfig = new SwaggerConfiguration()
                .prettyPrint(Boolean.TRUE)
                .resourcePackages(Sets.newHashSet(
                        SwaggerApiService.class.getPackage().getName()
                ));

        final OpenAPI openAPI = new JaxrsOpenApiContextBuilder()
                .openApiConfiguration(oasConfig)
                // 按ctxId缓存了
                .ctxId(Constants.APP_ID + "__all")
                .buildContext(true).read();

        if (openAPI.getInfo() == null) {
            final Info info = new Info();
            info.setTitle("OpenAPI definition");
            info.setVersion("v0");
            openAPI.setInfo(info);
        }

        return addCommonParameter(openAPI);
    }

    @SneakyThrows
    @Override
    public Object list(String category) {
        SwaggerConfiguration oasConfig = new SwaggerConfiguration()
                .prettyPrint(Boolean.TRUE)
                .resourcePackages(Sets.newHashSet(
                        SwaggerApiService.class.getPackage().getName() + "." + category)
                );

        final OpenAPI openAPI = new JaxrsOpenApiContextBuilder()
                .openApiConfiguration(oasConfig)
                // 按ctxId缓存了
                .ctxId(Constants.APP_ID + "category")
                .buildContext(true).read();

        if (openAPI.getInfo() == null) {
            final Info info = new Info();
            info.setTitle("OpenAPI definition");
            info.setVersion("v0");
            openAPI.setInfo(info);
        }

        return addCommonParameter(openAPI);
    }

    private OpenAPI addCommonParameter(OpenAPI openAPI) {
        openAPI.getPaths().forEach((path , pathItem) -> {
            for (Parameter parameter : paramters) {
                addCommonParameter(pathItem.getGet(), parameter);
                addCommonParameter(pathItem.getPost(), parameter);
                addCommonParameter(pathItem.getPut(), parameter);
                addCommonParameter(pathItem.getPatch(), parameter);
                addCommonParameter(pathItem.getDelete(), parameter);
                addCommonParameter(pathItem.getOptions(), parameter);
            }
        });
        return openAPI;
    }

    private void addCommonParameter(Operation operation, Parameter parameter) {
        if (operation == null) {
            return;
        }
        if (operation.getParameters() != null) {
            for (Parameter item : operation.getParameters()) {
                if (item.getName().equals(parameter.getName())) {
                    return;
                }
            }
        }
        operation.addParametersItem(parameter);
    }
}
