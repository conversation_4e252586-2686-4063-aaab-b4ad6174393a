package com.cisp.device;

import com.cvte.psd.conf.core.spring.annotation.EnableApolloConfig;
import com.seewo.honeycomb.rocketmq.annotation.EnableRocketMQProducer;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

@EnableApolloConfig
@Configuration
@SpringBootApplication
@EnableDubbo
@EnableRocketMQProducer
@EnableTransactionManagement(order = Integer.MAX_VALUE - 1)
public class DeviceApp {
    public static void main(String[] args) {

        SpringApplication.run(DeviceApp.class, args);
        System.out.println("----------------------->  Application start success <-------------------------");
    }

    @Bean
    public ApplicationRunner runner(DataSource dataSource) {
        return args -> {
            dataSource.getConnection().close();
        };
    }
}
