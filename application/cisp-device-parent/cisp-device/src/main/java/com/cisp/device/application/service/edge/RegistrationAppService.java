package com.cisp.device.application.service.edge;

import com.cisp.device.api.constant.DeviceTypeEnum;
import com.cisp.device.api.model.agent.acs.BasicInfo;
import com.cisp.device.api.model.notify.DeviceNotifyDto;
import com.cisp.device.api.model.vo.DeviceCreateVo;
import com.cisp.device.api.model.vo.DeviceUpdateVo;
import com.cisp.device.api.model.vo.DeviceVo;
import com.cisp.device.api.service.FacePanelApiService;
import com.cisp.device.application.service.DeviceAppService;
import com.cisp.device.infrastructure.convert.NotifyMapper;
import com.cisp.gateway.api.model.ApiEventDto;
import com.cisp.gateway.api.model.HttpReqDto;
import com.cisp.gateway.api.model.InvocationModelEnum;
import com.cisp.gateway.api.model.InvocationTypeEnum;
import com.cisp.gateway.sdk.ApiGatewayClient;
import com.cisp.gateway.sdk.annotation.EventListener;
import com.cisp.infrastructure.common.util.Jsons;
import com.cisp.infrastructure.common.util.TenantUtils;
import com.cisp.infrastructure.common.util.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "system.deploy.edge", havingValue = "true")
public class RegistrationAppService {

    @Resource
    private DeviceAppService appService;

    @Resource
    private NotifyMapper notifyMapper;

    @Resource
    ApiGatewayClient gatewayClient;

    @DubboReference(group = "unisee")
    private FacePanelApiService facePanelApiService;

//    @EventListener(topic = "device", tag = "registration", classes = DeviceCreateVo.class)
    public void handle(DeviceCreateVo createVo) {
        log.info("handle registration event: {}", Jsons.toJson(createVo));
        try {
            BasicInfo basicInfo = facePanelApiService.getBasicInfo(createVo.getIp(), createVo.getUsername(), createVo.getPassword());
            createVo.setSn(basicInfo.getSn());
            createVo.setDeviceId(basicInfo.getSn());
            appService.create(TenantUtils.getTenantId(), createVo);
            // 更新云端设备sn信息
            this.updateDevice(createVo.getId(), basicInfo.getSn());
            // 修改设备平台的地址
            if (Objects.nonNull(basicInfo)){
                facePanelApiService.modifyDataServer(basicInfo.getSn(), createVo.getIp());
            }
        }catch (Exception e){

        }
    }

    @EventListener(topic = "device", tag = "update", classes = DeviceNotifyDto.class)
    public void handle(DeviceNotifyDto notifyDto) {
        log.info("handle device update event: {}", Jsons.toJson(notifyDto));
        try {
            if ("CREATE".equals(notifyDto.getNotifyType())){
                notifyDto.getUpdateObjs().forEach(createDto -> {
                    DeviceCreateVo createVo = notifyMapper.toCreateVo(createDto);
                    BasicInfo basicInfo = facePanelApiService.getBasicInfo(createVo.getIp(), createVo.getUsername(), createVo.getPassword());
                    createVo.setSn(basicInfo.getSn());
                    createVo.setDeviceId(basicInfo.getSn());
                    appService.create(TenantUtils.getTenantId(), createVo);
                    // 更新云端设备sn信息
                    this.updateDevice(createVo.getId(), basicInfo.getSn());
                    // 修改设备平台的地址
                    if (Objects.nonNull(basicInfo)){
                        log.info("modify device {} platform address", createDto.getIp());
                        facePanelApiService.modifyDataServer(basicInfo.getSn(), createVo.getIp());
                    }
                });
            }else if ("UPDATE".equals(notifyDto.getNotifyType())){
                notifyDto.getUpdateObjs().forEach(updateDto -> {
                    DeviceUpdateVo updateVo = notifyMapper.toUpdateVo(updateDto);
                    if (DeviceTypeEnum.FACE_PANEL.getDesc().equals(updateDto.getType())){
                        DeviceVo deviceVo = appService.getDevice(updateDto.getId());
                        // 修改新的设备平台连接地址
                        if (StringUtils.isNotEmpty(updateVo.getIp()) && !deviceVo.getIp().equals(updateVo.getIp())){
                            BasicInfo basicInfo = facePanelApiService.getBasicInfo(updateVo.getIp(), updateVo.getUsername(), updateVo.getPassword());
                            if (Objects.nonNull(basicInfo)){
                                updateVo.setSn(basicInfo.getSn());
                                appService.update(updateDto.getId(), updateVo);
                                // 更新云端设备sn信息
                                this.updateDevice(updateDto.getId(), basicInfo.getSn());
                                facePanelApiService.modifyDataServer(basicInfo.getSn(), updateVo.getIp());
                                return;
                            }
                        }
                    }
                    appService.update(updateDto.getId(), updateVo);
                });
            }else if ("DELETE".equals(notifyDto.getNotifyType())){
                notifyDto.getUpdateObjs().forEach(delDto -> {
                    appService.delete(delDto.getId());
                });
            }
        }catch (Exception e){

        }
    }

    /**
     * 更新云端设备的sn信息
     * @param id
     * @param sn
     */
    private void updateDevice(Long id, String sn){
        HttpReqDto reqDto = new HttpReqDto()
                .setUrl("http://localhost:8080/cisp/device/edge/devices/" + id)
                .setMethod("PUT")
                .setBody(new DeviceUpdateVo().setSn(sn));
        ApiEventDto eventDto = new ApiEventDto<>()
                .setSource(TenantUtils.getTenantId())
                .setInvocationModel(InvocationModelEnum.NORTH.getCode())
                .setInvocationType(InvocationTypeEnum.SYNC.getCode())
                .setData(reqDto)
                .setTime(TimeUtils.timestampToDateTimeString(System.currentTimeMillis()));
        gatewayClient.call(eventDto, Object.class);
    }

}
