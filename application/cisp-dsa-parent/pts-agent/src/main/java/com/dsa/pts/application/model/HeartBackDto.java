package com.dsa.pts.application.model;

import com.cisp.infrastructure.common.util.DateTimeConverter;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class HeartBackDto {

	public HeartBackDto(String url, String code, Date time) {
		this.responseUrl = url;
		this.code = code;
		this.data = new Data(time);
	}

	@JsonProperty("ResponseURL")
	private String responseUrl;

	@JsonProperty("Code")
	private String code;

	@JsonProperty("Data")
	private Data data;

	class Data {
		@JsonProperty("Time")
		@JsonSerialize(using = DateTimeConverter.SerializeDate.class)
		Date time;

		Data(Date time) {
			this.time = time;
		}

		public Date getTime() {
			return time;
		}

		public void setTime(Date time) {
			this.time = time;
		}
	}

	@Override
	public String toString() {
		return "HeartBackDO{" +
				"responseUrl='" + responseUrl + '\'' +
				", code=" + code +
				", data=" + data +
				'}';
	}

}
