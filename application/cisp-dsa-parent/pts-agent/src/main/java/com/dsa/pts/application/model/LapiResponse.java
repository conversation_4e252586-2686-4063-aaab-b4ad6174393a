package com.dsa.pts.application.model;

import com.fasterxml.jackson.annotation.JsonProperty;

public class LapiResponse<T> {

	@JsonProperty("ResponseURL")
	private String responseUrl;

	@JsonProperty("CreatedID")
	private Integer createID;

	@JsonProperty("ResponseCode")
	private Integer responseCode;

	@JsonProperty("ResponseString")
	private String responseString;

	@JsonProperty("StatusCode")
	private Integer statusCode;

	@JsonProperty("StatusString")
	private String  statusString;

	@JsonProperty("Data")
	T data;

	public String getResponseUrl() {
		return responseUrl;
	}

	public void setResponseUrl(String responseUrl) {
		this.responseUrl = responseUrl;
	}

	public Integer getStatusCode() {
		return statusCode;
	}

	public void setStatusCode(Integer statusCode) {
		this.statusCode = statusCode;
	}

	public String getStatusString() {
		return statusString;
	}

	public void setStatusString(String statusString) {
		this.statusString = statusString;
	}

	public T getData() {
		return data;
	}

	public void setData(T data) {
		this.data = data;
	}

	public Integer getCreateID() {
		return createID;
	}

	public void setCreateID(Integer createID) {
		this.createID = createID;
	}

	public Integer getResponseCode() {
		return responseCode;
	}

	public void setResponseCode(Integer responseCode) {
		this.responseCode = responseCode;
	}

	public String getResponseString() {
		return responseString;
	}

	public void setResponseString(String responseString) {
		this.responseString = responseString;
	}
}
