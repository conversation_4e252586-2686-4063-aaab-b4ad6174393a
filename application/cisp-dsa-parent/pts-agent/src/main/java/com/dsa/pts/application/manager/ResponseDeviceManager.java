package com.dsa.pts.application.manager;

import com.cisp.infrastructure.common.util.Jsons;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.http.DefaultFullHttpResponse;
import io.netty.handler.codec.http.FullHttpResponse;
import io.netty.handler.codec.http.HttpHeaderValues;

import java.util.Objects;

import static io.netty.handler.codec.http.HttpResponseStatus.OK;
import static io.netty.handler.codec.http.HttpVersion.HTTP_1_1;

/**
 * @description 回复设备消息业务类
 */
public class ResponseDeviceManager {


	private static final String CONTENT_LENGTH = "Content-Length";
	private static final String CONTENT_TYPE = "Content-Type";
	private static final String CONNECTION = "Connection";
	private static final String ACCESS_CONTROL_ALLOW_HEADERS = "Access-Control-Allow-Headers";
	private static final String X_FRAME_OPTIONS_HEADERS = "X-Frame-Options";

	/**
	 * 设置响应消息体
	 *
	 * @param object
	 */
	public static FullHttpResponse setResponseBody(Object object) {
		String body = "";
		FullHttpResponse response = null;
		if (Objects.nonNull(object)) {
			body = Jsons.toJson(object);
			response = new DefaultFullHttpResponse(HTTP_1_1, OK, Unpooled.wrappedBuffer(body.getBytes()));
		} else {
			response = new DefaultFullHttpResponse(HTTP_1_1, OK);
		}
		return response;
	}

	/**
	 * @param ctx    连接通道
	 * @param object 响应信息
	 * @param isHeartBeat 是否为心跳，因为心跳时有特殊的头进行设置
	 */
	public static void responseDevice(ChannelHandlerContext ctx, Object object, Boolean isHeartBeat) {
		FullHttpResponse response = setResponseBody(object);
		setResponseHeaders(response, isHeartBeat);
		ctx.writeAndFlush(response);
	}

	/**
	 * 设置回复消息体header信息
	 *  CONTENT_TYPE 如果不一致，可自定义传参
	 * @param response
	 * @return
	 */
	public static FullHttpResponse setResponseHeaders(FullHttpResponse response, Boolean isHeartBeat) {
		response.headers().set(CONTENT_LENGTH, response.content().readableBytes());
		response.headers().set(CONTENT_TYPE, HttpHeaderValues.TEXT_PLAIN);
		response.headers().set(CONNECTION, HttpHeaderValues.CLOSE);
		if (isHeartBeat){
			response.headers().set(X_FRAME_OPTIONS_HEADERS, "SAMEORIGIN");
		}else {
			response.headers().set(ACCESS_CONTROL_ALLOW_HEADERS, HttpHeaderValues.CLOSE);
		}
		return response;
	}
}
