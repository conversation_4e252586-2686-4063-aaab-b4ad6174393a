package com.dsa.pts.application.netty.ptotocol;

import com.cisp.device.api.constant.Constants;
import com.cisp.device.api.service.DeviceApiService;
import com.dsa.pts.application.netty.factory.ConnectionStore;
import com.dsa.pts.application.netty.handler.ServerChannel;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProtocolProcessorMgt {

    @Resource
    private ConnectionStore connectionStore;

    @DubboReference
    private DeviceApiService deviceApiService;

    private Map<String, ProtocolProcessor> processorMap;

    public ProtocolProcessorMgt(List<ProtocolProcessor> processors) {
        processorMap = new ConcurrentHashMap<>();
        processors.forEach(processor -> processorMap.put(processor.getKey(), processor));
    }

    /**
     * 处理设备主动过来的请求
     * @param uri
     * @param serverChannel
     * @param message
     */
    public void process(String uri, ServerChannel serverChannel, String message){
        ProtocolProcessor processor = processorMap.get(uri);
        if (Objects.nonNull(processor)){
            processor.process(serverChannel, message);
        }else {
            log.error("{} processor not exists", uri);
        }
    }

    public void offline(ServerChannel serverChannel){
        connectionStore.removeConnection(serverChannel.serialNo());
        serverChannel.close();
        deviceApiService.updateDeviceStatus(serverChannel.deviceId(), Constants.DEVICE_STATUS_OFFLINE);
    }

}
