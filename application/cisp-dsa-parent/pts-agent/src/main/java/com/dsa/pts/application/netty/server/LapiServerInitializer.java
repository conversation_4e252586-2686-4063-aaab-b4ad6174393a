package com.dsa.pts.application.netty.server;

import cn.hutool.extra.spring.SpringUtil;
import com.dsa.pts.application.netty.codec.HttpDecoder;
import com.dsa.pts.application.netty.codec.HttpEncoder;
import com.dsa.pts.application.netty.handler.IdleTimeoutHandler;
import com.dsa.pts.application.netty.handler.LapiServerHandler;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.http.HttpObjectAggregator;
import io.netty.handler.timeout.IdleStateHandler;

import java.util.concurrent.TimeUnit;

public class LapiServerInitializer extends ChannelInitializer<SocketChannel> {

    private final int READ_TIME_OUT = 60;

    @Override
    protected void initChannel(SocketChannel ch) throws Exception {
        final IdleTimeoutHandler timeoutHandler = new IdleTimeoutHandler();
        ChannelPipeline pipeline = ch.pipeline();
        //通道空闲超时时间
        pipeline.addLast("idleStateHandler", new IdleStateHandler(READ_TIME_OUT, READ_TIME_OUT, READ_TIME_OUT, TimeUnit.SECONDS));
        pipeline.addAfter("idleStateHandler", "idleEventHandler", timeoutHandler);
        pipeline.addLast(new HttpDecoder());
        pipeline.addLast(new HttpEncoder());
        pipeline.addLast("aggregator", new HttpObjectAggregator(10 * 1024 * 1024));
        //心跳
        LapiServerHandler lapiServerHandler = SpringUtil.getBean(LapiServerHandler.class);
        pipeline.addLast(lapiServerHandler);
    }
}
