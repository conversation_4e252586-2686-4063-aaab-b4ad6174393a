package com.dsa.pts.application.model.timetemplate;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class UExceptionDay {
    @JsonProperty("ID")
    private Integer id;
    @JsonProperty("Enabled")
    private Integer enabled;
    @JsonProperty("Date")
    private String date;
    @JsonProperty("Num")
    private Integer num;
    @JsonProperty("TimeSectionInfos")
    private List<UTimeSectionInfo> timeSectionInfos;
}