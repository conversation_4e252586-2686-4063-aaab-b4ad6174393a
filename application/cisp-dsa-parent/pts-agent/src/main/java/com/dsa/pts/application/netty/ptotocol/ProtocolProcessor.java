package com.dsa.pts.application.netty.ptotocol;

import com.dsa.pts.application.netty.handler.ServerChannel;

/**
 * 协议处理器接口
 * <AUTHOR>
 */
public interface ProtocolProcessor {

    /**
     * 获取协议协议器标识
     * @return
     */
    String getKey();

    /**
     * 处理设备->平台的请求
     * 例如心跳、过人记录推送
     * @param serverChannel
     * @param message
     */
    void process(ServerChannel serverChannel, String message);

}
