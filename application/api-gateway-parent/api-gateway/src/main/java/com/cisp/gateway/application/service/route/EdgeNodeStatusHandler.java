package com.cisp.gateway.application.service.route;

import com.cisp.gateway.acl.IotAclService;
import com.cisp.gateway.api.constant.Constants;
import com.cisp.gateway.api.model.EdgeNodeInfo;
import com.cisp.infrastructure.common.jsonpath.JsonPathHelper;
import com.cisp.infrastructure.mq.MqProducer;
import com.seewo.framework.mq.client.model.Message;
import com.seewo.honeycomb.rocketmq.annotation.MQBody;
import com.seewo.honeycomb.rocketmq.annotation.MQListener;
import com.seewo.honeycomb.rocketmq.annotation.Tags;
import com.seewo.honeycomb.rocketmq.annotation.Topic;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.Map;

@Slf4j
@Service
@ConditionalOnProperty(name = {"module.api-gateway.deployModel"}, havingValue = "cloud")
@MQListener
@Topic(value = Constants.IOT_STATUS_TOPIC)
public class EdgeNodeStatusHandler {

    @Resource
    private IotAclService iotAclService;

    @Resource
    private MqProducer mqProducer;

    @Tags(value = Constants.IOT_STATUS_TOPIC_TAG)
    public void thingStatusChange(@MQBody Map<String, Object> msg, Message message) {
        log.info("Received device status change message, msg: {}", msg);
        EdgeNodeInfo edgeNodeInfo = iotAclService.getEdgeDeviceInfo(BigInteger.valueOf(Long.valueOf(JsonPathHelper.getString(msg, "deviceId"))));
        mqProducer.sendMsg("cispDeviceStatusUpdate", "api-gateway", "api-gateway", edgeNodeInfo);
    }
}