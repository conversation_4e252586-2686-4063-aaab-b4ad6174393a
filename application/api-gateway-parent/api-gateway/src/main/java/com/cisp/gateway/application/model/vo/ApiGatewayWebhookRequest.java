package com.cisp.gateway.application.model.vo;

import com.seewo.open.sdk.OpenApiRequest;

/**
 * seewo-open API: API网关webhook
 * test
 *
 * <AUTHOR> create
 * @since 2.0.1 2025-5-22
 */
public class ApiGatewayWebhookRequest extends OpenApiRequest<ApiGatewayWebhookParam, ApiGatewayWebhookResult> {

    public ApiGatewayWebhookRequest(String domain, String reqPath,ApiGatewayWebhookParam param) {
        this(domain, reqPath);
        setBizModel(param);
    }

    public ApiGatewayWebhookRequest(String domain, String reqPath) {
        setServerUrl(domain);
        setPath(reqPath);
        setHttpMethod("POST");
        // replace with your permissionId
        setPermissionId("");
    }

    public Class<ApiGatewayWebhookResult> getResponseClass() {
        return ApiGatewayWebhookResult.class;
    }

    public Class<ApiGatewayWebhookParam> getDomainClass() {
        return ApiGatewayWebhookParam.class;
    }
}

