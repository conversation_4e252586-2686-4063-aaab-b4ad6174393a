package com.cisp.gateway.application.service.route;

import cn.hutool.core.map.MapUtil;
import com.cisp.gateway.api.model.HttpReqDto;
import com.cisp.gateway.infrastructure.config.GatewayConfig;
import com.cisp.infrastructure.common.util.Jsons;
import com.google.common.collect.Maps;
import com.seewo.kishframework.exception.PlatformExceptionCode;
import com.seewo.kishframework.rest.RestResponse;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * http请求分发
 * 1、边缘向云端调用；
 * 2、云端/边缘情况下内部调用
 */
@Slf4j
@Service
public class HttpDispatcher {

    @Resource(name = "httpClient")
    private OkHttpClient httpClient;

    @Resource
    private GatewayConfig config;

    /**
     * 请求分发
     * @param tenantId 租户ID
     * @param httpReqDto HTTP请求DTO
     * @return 响应结果
     */
    public Object dispatch(String tenantId, HttpReqDto httpReqDto){
        if (MapUtil.isEmpty(httpReqDto.getHeaders())){
            httpReqDto.setHeaders(new  HashMap<>());
        }
        httpReqDto.getHeaders().put("x-tenant-id", tenantId);
        httpReqDto.getHeaders().put("x-sw-app-id", config.openapiAppid());
        switch (httpReqDto.getMethod()){
            case "GET":
                return doGet(httpReqDto.getUrl(), httpReqDto.getHeaders());
            case "POST":
                return doPost(httpReqDto.getUrl(), httpReqDto.getBody(), httpReqDto.getHeaders());
            case "PUT":
                return doUpdate(httpReqDto.getUrl(), httpReqDto.getBody(), httpReqDto.getHeaders());
            case "DELETE":
                return doDelete(httpReqDto.getUrl(), httpReqDto.getHeaders());
            default:
                log.warn("Unsupported HTTP method: {}", httpReqDto.getMethod());
                break;
        }
        return null;
    }

    /**
     * 执行GET请求
     *
     * @param url 请求URL
     * @param headers 请求头
     * @return 响应结果
     */
    private Object doGet(String url, Map<String, String> headers){
        Request request = new Request.Builder()
                .url(url)
                .headers(Headers.of(headers))
                .build();
        return executeRequest(request, url, "GET");
    }

    /**
     * 执行POST请求
     *
     * @param url 请求URL
     * @param body 请求体
     * @param headers 请求头
     * @return 响应结果
     */
    private Object doPost(String url, Object body, Map<String, String> headers){
        RequestBody requestBody = createRequestBody(body);
        Request request = new Request.Builder()
                .url(url)
                .headers(Headers.of(headers))
                .post(requestBody)
                .build();
        return executeRequest(request, url, "POST");
    }

    /**
     * 执行PUT请求
     *
     * @param url 请求URL
     * @param body 请求体
     * @param headers 请求头
     * @return 响应结果
     */
    private Object doUpdate(String url, Object body, Map<String, String> headers){
        RequestBody requestBody = createRequestBody(body);
        Request request = new Request.Builder()
                .url(url)
                .headers(Headers.of(headers))
                .put(requestBody)
                .build();
        return executeRequest(request, url, "PUT");
    }

    /**
     * 执行DELETE请求
     *
     * @param url 请求URL
     * @param headers 请求头
     * @return 响应结果
     */
    private Object doDelete(String url, Map<String, String> headers){
        Request request = new Request.Builder()
                .url(url)
                .headers(Headers.of(headers))
                .delete()
                .build();
        return executeRequest(request, url, "DELETE");
    }

    /**
     * 创建请求体
     *
     * @param body 请求体对象
     * @return RequestBody
     */
    private RequestBody createRequestBody(Object body) {
        if (body == null) {
            // 创建一个空的请求体
            return RequestBody.create(MediaType.parse("application/json"), "");
        }
        if (body instanceof String) {
            return RequestBody.create(MediaType.parse("application/json"), (String) body);
        } else {
            // 将对象转换为JSON字符串
            String jsonBody = Jsons.toJson(body);
            return RequestBody.create(MediaType.parse("application/json"), jsonBody);
        }
    }

    /**
     * 执行HTTP请求并处理响应
     *
     * @param request 请求对象
     * @param url 请求URL
     * @param method 请求方法
     * @return 响应结果
     */
    private Object executeRequest(Request request, String url, String method) {
        Response response = null;
        try {
            response = httpClient.newCall(request).execute();
            if (response.isSuccessful()){
                String responseBody = response.body().string();
                RestResponse restResponse = Jsons.strToObj(responseBody, RestResponse.class);
                if (restResponse.isSuccess()){
                    return restResponse;
                }else {
                    throw PlatformExceptionCode.COMMON_SYSTEM_EXCEPTION.exception("请求失败,%s", restResponse.getMessage());
                }
            } else {
                throw PlatformExceptionCode.COMMON_SYSTEM_EXCEPTION.exception("请求失败,%s", response.message());
            }
        } catch (Exception e){
            throw PlatformExceptionCode.COMMON_SYSTEM_EXCEPTION.exception("请求失败,%s", e.getMessage());
        } finally {
            if (Objects.nonNull(response)){
                response.close();
            }
        }
    }
}
