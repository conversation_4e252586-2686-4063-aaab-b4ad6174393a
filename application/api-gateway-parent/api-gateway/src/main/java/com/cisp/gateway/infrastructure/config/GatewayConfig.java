package com.cisp.gateway.infrastructure.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.cisp.infrastructure.common.model.bo.DeviceTypeVo;
import com.seewo.open.sdk.DefaultSeewoClient;
import com.seewo.open.sdk.SeewoClient;
import com.seewo.open.sdk.auth.Account;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "module.api-gateway")
public class GatewayConfig {

    private Boolean enable = true;

    /** 边缘节点的标识 */
//    private String sn = "";

    /** 部署模式，云端还是边缘，cloud/edge */
    private String deployModel = "cloud";

    private Iot iot = new Iot();

    private Openapi openapi = new Openapi();

    public String getIotAccessKey(){
        return iot.getAccessKey();
    }
    public String getIotSecret(){
        return iot.getSecretKey();
    }

    public String getIotEdgeClientProductKey() {
        return iot.getEdgeClientProductKey();
    }

    public String getIotEdgeClientSecret() {
        return iot.getEdgeClientSecret();
    }

    public String getIotBroker(){
        return iot.getBroker();
    }

    public String getRegisterUrl(){
        return iot.getRegisterUrl();
    }

    public String getGrailUrl() {
        return iot.getGrailUrl();
    }

    public String getGrailEnv(){
        return iot.getGrailEnv();
    }

    public Map<String, DeviceTypeVo> getDeviceTypeMap(){
        return JSON.parseObject(iot.getDeviceType(), new TypeReference<Map<String, DeviceTypeVo>>(){});
    }

    public String openapiDomain(){
        return openapi.getDomain();
    }

    public String getReqPath(){
        return openapi.getReqPath();
    }

    public String openapiAppid(){
        return openapi.getAppid();
    }
    public String openapiSecret(){
        return openapi.getSecret();
    }

    @Data
    public class Iot{
        private String host = "";
        private String accessKey = "";
        private String secretKey = "";
        private String deviceType="";
        private String broker = "";
        private String edgeClientProductKey = "";
        private String edgeClientSecret = "";
        private String registerUrl = "";
        private String grailUrl = "";
        private String grailEnv = "";
        /** 边缘客户端设备ID是否自动加入分组 */
        private Boolean autoToGroup = false;
    }

    @Data
    public class Openapi{
        private String domain = "https://openapi.seewo.com";
        private String appid = "";
        private String secret = "";
        private String reqPath = "/cisp-server/cisp-gateway-dev/api/gateway/webhook";
    }

    @Bean
    public SeewoClient seewoClient() {
        return new DefaultSeewoClient(new Account(openapi.getAppid(), openapi.getSecret()));
    }

}
