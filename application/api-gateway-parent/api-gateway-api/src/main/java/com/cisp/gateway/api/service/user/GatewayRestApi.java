package com.cisp.gateway.api.service.user;

import com.cisp.gateway.api.annotation.BusiFuncBind;
import com.cisp.gateway.api.constant.BusiFuncMeta;
import com.cisp.gateway.api.constant.Constants;
import com.cisp.gateway.api.model.ApiEventDto;
import com.seewo.kishframework.rest.RestResponse;
import com.seewo.kishframework.rest.VoidRestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;

import javax.validation.constraints.NotNull;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;


@Tag(name = "GatewayRestApi", description = "API网关API接口")
@Path(Constants.API_PREFIX)
@Produces({MediaType.APPLICATION_JSON})
public interface GatewayRestApi {

    @Operation(summary = "test")
    @POST
    @Path("/test")
    RestResponse<Object> test(@NotNull(message = "请求参数不能为空") ApiEventDto request);

    @Operation(summary = "API网关请求接口")
    @POST
    @Path("/webhook")
    @BusiFuncBind(BusiFuncMeta.COMMON_AUTHENTICATION_FUNC)
    RestResponse<Object> webhook(@NotNull(message = "请求参数不能为空") ApiEventDto request);

}
