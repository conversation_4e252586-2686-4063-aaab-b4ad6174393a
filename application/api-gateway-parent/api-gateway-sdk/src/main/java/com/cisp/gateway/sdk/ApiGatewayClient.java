package com.cisp.gateway.sdk;

import com.cisp.gateway.api.model.ApiEventDto;
import com.cisp.gateway.sdk.acl.ApiGatewayApiService;
import com.cisp.gateway.sdk.annotation.EventListener;
import com.cisp.infrastructure.common.util.Jsons;
import com.seewo.kishframework.exception.PlatformExceptionCode;
import com.seewo.kishframework.rest.RestResponse;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.exception.MQBrokerException;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.exception.RemotingException;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Map;

/**
 * API 网关客户端
 * 提供与 API 网关交互的功能
 */
@Slf4j
//@SpringBootApplication
public class ApiGatewayClient /*implements ApplicationRunner*/ {

    private final ApiGatewayApiService gatewayApiService;

    public ApiGatewayClient(ApiGatewayApiService gatewayApiService) {
        this.gatewayApiService = gatewayApiService;
    }

//    public static void main(String[] args) {
//        SpringApplication.run(ApiGatewayClient.class, args);
//    }

    /**
     * 向API网关发送事件消息
     * @param eventDto
     */
    public void publish(ApiEventDto eventDto){
        this.gatewayApiService.webhook(eventDto);
    }

    /**
     * 调用API网关，有返回值
     * @param eventDto
     * @param clazz
     * @return
     * @param <T>
     */
    public <T> T call(ApiEventDto eventDto, Class<T> clazz){
        RestResponse<Object> response = this.gatewayApiService.webhook(eventDto);
        if (response.isSuccess()){
            return Jsons.objToObj(response.getData(), clazz);
        }else {
            throw PlatformExceptionCode.COMMON_SYSTEM_EXCEPTION.exception(response.getMessage());
        }
    }

//    @Override
//    public void run(ApplicationArguments args) throws Exception {
//        DefaultMQProducer producer = new DefaultMQProducer("api-gateway");
//        producer.setNamesrvAddr("************:9876"); // 设置为实际的 RocketMQ 服务器地址
//
//        try {
//            producer.start();
//
//            String deviceMessage = "{\"deviceId\":\"device-001\",\"deviceType\":\"camera\",\"status\":\"online\","
//                    + "\"statusTime\":\"" + System.currentTimeMillis() + "\",\"extraInfo\":\"IP: *************\"}";
//            Message deviceMsg = new Message(
//                    "DeviceStatusTopic",
//                    "device-status-change",
//                    deviceMessage.getBytes(StandardCharsets.UTF_8)
//            );
//            SendResult deviceResult = producer.send(deviceMsg);
//            log.info("Sent device status message, result: {}", deviceResult);
//
//            String deviceMessage1 = "{\"deviceId\":\"device-001\",\"deviceType\":\"camera\",\"status\":\"online\","
//                    + "\"statusTime\":\"" + System.currentTimeMillis() + "\",\"extraInfo\":\"IP: *************\"}";
//            Message deviceMsg1 = new Message(
//                    "DeviceStatusTopic",
//                    "device-change-1",
//                    deviceMessage1.getBytes(StandardCharsets.UTF_8)
//            );
//            SendResult deviceResult1 = producer.send(deviceMsg1);
//            log.info("Sent device status message1, result: {}", deviceResult1);
//
//            String deviceTestMessage = "test" + System.currentTimeMillis();
//            Message deviceTestMsg = new Message(
//                    "DeviceTestTopic",
//                    "device-test",
//                    deviceTestMessage.getBytes(StandardCharsets.UTF_8)
//            );
//            log.info("Sent device test message:{}", deviceTestMessage);
//            SendResult deviceTestResult = producer.send(deviceTestMsg);
//            log.info("Sent device test message, result: {}", deviceTestResult);
//
//        } catch (MQClientException | RemotingException | MQBrokerException | InterruptedException e) {
//            log.error("Failed to send test messages", e);
//        } finally {
//            producer.shutdown();
//        }
//    }
//
//    @EventListener(topic = "DeviceStatusTopic", tag = "device-change-1", classes = Map.class)
//    public void deviceStatusChange1(Map message) {
//        log.info("Received device status change-1 message: {}", message);
//    }
//
//    @EventListener(topic = "DeviceStatusTopic", tag = "device-status-change", classes = Map.class)
//    public void deviceStatusChange(Map message) {
//        log.info("Received device status change message: {}", message);
//    }
//
//    @EventListener(topic = "DeviceTestTopic", tag = "device-test")
//    public void deviceStatusTest(String message) {
//        log.info("Received device test  message: {}", message);
//    }

}
