<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <properties resource="generator/config.properties"/>

    <context id="Mysql" targetRuntime="MyBatis3Simple" defaultModelType="flat">
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

        <plugin type="${mapper.plugin}">
            <property name="mappers" value="${mapper.Mapper}"/>
        </plugin>

        <jdbcConnection driverClass="${jdbc.driverClass}"
                        connectionURL="${jdbc.url}"
                        userId="${jdbc.user}"
                        password="${jdbc.password}">
        </jdbcConnection>

        <javaModelGenerator targetPackage="${targetModelPackage}" targetProject="${mybatis.generator.basedir}${targetJavaProject}">
            <property name="rootClass" value="com.seewo.kishframework.mybatis.base.BaseEntity" />
        </javaModelGenerator>

        <sqlMapGenerator targetPackage="${targetXMLPackage}"  targetProject="${mybatis.generator.basedir}${targetResourcesProject}"/>

        <javaClientGenerator targetPackage="${targetMapperPackage}" targetProject="${mybatis.generator.basedir}${targetJavaProject}" type="XMLMAPPER" />

        <!-- https://mybatis.org/generator/configreference/table.html -->
        <!--<table tableName="idt_group" domainObjectName="GroupEntity" mapperName="GroupRepository">
        </table>-->
    </context>
</generatorConfiguration>