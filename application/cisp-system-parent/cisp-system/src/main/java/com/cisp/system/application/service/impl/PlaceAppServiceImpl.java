package com.cisp.system.application.service.impl;

import com.cisp.system.acl.PlaceAclService;
import com.cisp.system.application.model.bo.PlaceBo;
import com.cisp.system.application.service.PlaceAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class PlaceAppServiceImpl implements PlaceAppService {
    @Autowired
    private PlaceAclService placeAclService;

    @Override
    public List<PlaceBo> getPlaceTree(String tenantId) {
        return placeAclService.getPlaceTree(tenantId);
    }

    @Override
    public List<PlaceBo> getUserManagedPlaces(String userId, String tenantId) {
        return placeAclService.getUserManagedPlaces(userId, tenantId);
    }
}
