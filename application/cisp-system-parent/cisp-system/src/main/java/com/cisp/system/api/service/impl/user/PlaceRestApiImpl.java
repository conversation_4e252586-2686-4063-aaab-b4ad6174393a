package com.cisp.system.api.service.impl.user;


import com.cisp.infrastructure.common.context.ContextUtils;
import com.cisp.system.api.model.vo.PlaceTreeVo;
import com.cisp.system.api.model.vo.PlaceVo;
import com.cisp.system.api.service.user.PlaceRestApi;
import com.cisp.system.application.model.bo.PlaceBo;
import com.cisp.system.application.service.PlaceAppService;
import com.cisp.system.infrastructure.convert.PlaceMapper;
import com.seewo.kishframework.rest.RestResponse;
import com.seewo.kishframework.security.SecurityUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@DubboService(protocol = "rest", register = false, validation = "true")
public class PlaceRestApiImpl implements PlaceRestApi {
    @Autowired
    private PlaceAppService placeAppService;
    @Autowired
    private PlaceMapper placeMapper;

    @Override
    public RestResponse<List<PlaceTreeVo>> getPlaceTree() {
        List<PlaceBo> placeBos = placeAppService.getPlaceTree(ContextUtils.get(ContextUtils.TENANT_KEY));
        return RestResponse.success(placeMapper.fromPlaceBo(placeBos));
    }

    @Override
    public RestResponse<List<PlaceVo>> getUserManagedPlaces() {
        List<PlaceBo> places = placeAppService.getUserManagedPlaces(SecurityUtils.context().getUserId(), ContextUtils.get(ContextUtils.TENANT_KEY));
        return RestResponse.success(placeMapper.to(places));
    }
}
