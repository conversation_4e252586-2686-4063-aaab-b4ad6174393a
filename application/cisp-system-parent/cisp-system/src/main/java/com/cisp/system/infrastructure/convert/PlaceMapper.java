package com.cisp.system.infrastructure.convert;

import com.cisp.system.api.model.dto.PlaceDto;
import com.cisp.system.api.model.dto.PlaceTreeNodeDto;
import com.cisp.system.api.model.vo.PlaceTreeVo;
import com.cisp.system.api.model.vo.PlaceVo;
import com.cisp.system.application.model.bo.PlaceBo;
import com.seewo.mdm.base.api.pl.BuildingDto;
import com.seewo.mdm.base.api.pl.BuildingRoomDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

@Mapper(componentModel = "spring")
public interface PlaceMapper {

    @Mappings({
            @Mapping(source = "uid", target = "id"),
            @Mapping(source = "administratorUids", target = "directorIds"),
    })
    PlaceBo fromBuildDto(BuildingDto buildingDto);

    PlaceVo to(PlaceBo placeBo);


    @Mappings({
            @Mapping(source = "uid", target = "id"),
            @Mapping(source = "buildingUid", target = "parentId"),
    })
    PlaceBo fromBuildRoomDto(BuildingRoomDto buildingRoomDto);

    @Mappings({
            @Mapping(source = "name", target = "label")
    })
    PlaceTreeVo fromPlaceBo(PlaceBo placeBo);

    PlaceTreeNodeDto from(PlaceBo placeBo);

    PlaceDto toDto(PlaceBo placeBo);
    List<PlaceTreeNodeDto> from(List<PlaceBo> placeBos);

    List<PlaceDto> toDto(List<PlaceBo> placeBos);
    List<PlaceTreeVo> fromPlaceBo(List<PlaceBo> placeBos);

    List<PlaceBo> fromBuildDto(List<BuildingDto> buildingDtos);

    List<PlaceBo> fromBuildRoomDto(List<BuildingRoomDto> buildingRoomDtos);

    List<PlaceVo> to(List<PlaceBo> placeBos);
}
