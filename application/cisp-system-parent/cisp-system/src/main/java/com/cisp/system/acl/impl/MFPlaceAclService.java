package com.cisp.system.acl.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.cisp.system.acl.PlaceAclService;
import com.cisp.system.application.model.bo.PlaceBo;
import com.cisp.system.infrastructure.convert.PlaceMapper;
import com.seewo.mdm.base.api.BuildingApi;
import com.seewo.mdm.base.api.BuildingRoomApi;
import com.seewo.mdm.base.api.pl.*;
import com.seewo.mis.common.response.BaseResponse;
import com.seewo.mis.common.response.DataPage;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static java.util.Collections.emptyList;

@Service
@Slf4j
public class MFPlaceAclService implements PlaceAclService {

    @Value("${kish.security.appCode:cisp-ops}")
    private String appCode;
    @DubboReference
    private BuildingApi buildingApi;
    @DubboReference
    private BuildingRoomApi buildingRoomApi;
    @Autowired
    private PlaceMapper placeMapper;


    /**
     * MF为两级结构
     * @param tenantId
     * @return
     */
    @Override
    public List<PlaceBo> getPlaceTree(String tenantId) {
        List<BuildingDto> buildingDtos = getBuildingDtos(tenantId);
        List<BuildingRoomDto> buildingRoomDtos = getBuildingRoomDtos(tenantId);
        List<PlaceBo> result = new ArrayList<>(20);
        buildingDtos.forEach(buildingDto -> {
            PlaceBo placeBo = placeMapper.fromBuildDto(buildingDto);
            List<BuildingRoomDto> rooms = buildingRoomDtos.stream().filter(room -> room.getBuildingUid().equals(buildingDto.getUid())).collect(Collectors.toList());
            placeBo.setChildren(placeMapper.fromBuildRoomDto(rooms));
            result.add(placeBo);
        });
        return result;
    }

    @Override
    public List<PlaceBo> getUserManagedPlaces(String userId, String tenantId) {
        List<BuildingDto> buildingDtos = getBuildingDtos(tenantId)
                .stream()
                .filter(buildingDto -> buildingDto.getAdministratorUids().contains(userId))
                .collect(Collectors.toList());
        return placeMapper.fromBuildDto(buildingDtos);
    }

    @Override
    public List<PlaceBo> getPlaces(String tenantId,List<String> ids) {
        List<PlaceBo> result = new ArrayList<>(20);
        result.addAll(getBuildingPlace(tenantId,ids));
        result.addAll(getBuildingRoomPlace(tenantId,ids));
        return result;
    }

    @Override
    public List<PlaceBo> getPlaces(String tenantId, String parentId) {
        if(StrUtil.isEmpty(parentId)){
            List<BuildingDto> buildingDtos = getBuildingDtos(tenantId);
            return placeMapper.fromBuildDto(buildingDtos);
        }
        return getBuildingRoomByBuildingId(tenantId,parentId);
    }

    @Override
    public PlaceBo getPlace(String tenantId, String id) {
        List<PlaceBo> places = getPlaces(tenantId, Arrays.asList(id));
        return CollectionUtil.isNotEmpty(places)? places.get(0) : null;
    }

    private List<PlaceBo> getBuildingRoomByBuildingId(String tenantId,String parentId){
        QueryBuildingRoomsByBuildingUidReqDto reqDto = new QueryBuildingRoomsByBuildingUidReqDto();
        reqDto.setAppKey(appCode);
        reqDto.setOrgUid(tenantId);
        reqDto.setBuildingUid(parentId);
        BaseResponse<DataPage<BuildingRoomDto>> response = buildingRoomApi.queryBuildingRoomsByBuildingUid(reqDto);
        if(response.success()){
            return placeMapper.fromBuildRoomDto(response.getData().getContent());
        }
        return emptyList();
    }

    private List<PlaceBo> getBuildingPlace(String tenantId,List<String> ids){
        GetBuildingDto buildingDto = new GetBuildingDto();
        buildingDto.setAppKey(appCode);
        buildingDto.setOrgUid(tenantId);
        buildingDto.setUids(ids);
        BaseResponse<List<BuildingDto>> response = buildingApi.queryBuildingByIds(buildingDto);
        if(response.success()){
            return placeMapper.fromBuildDto(response.getData());
        }
        return emptyList();
    }

    private List<PlaceBo> getBuildingRoomPlace(String tenantId,List<String> ids){
        ListBuildingRoomsByIdsReqDto reqDto = new ListBuildingRoomsByIdsReqDto();
        reqDto.setAppKey(appCode);
        reqDto.setBuildingRoomUids(ids);
        reqDto.setOrgUid(tenantId);
        BaseResponse<List<BuildingRoomDto>> baseResponse = buildingRoomApi.listBuildingRoomsByUids(reqDto);
        if(baseResponse.success()){
            return placeMapper.fromBuildRoomDto(baseResponse.getData());
        }
        return emptyList();
    }

    private List<BuildingDto> getBuildingDtos(String tenantId){
        GetBuildingDto buildingDto = new GetBuildingDto();
        buildingDto.setOrgUid(tenantId);
        buildingDto.setAppKey(appCode);
        BaseResponse<List<BuildingDto>> response = buildingApi.queryBuildingsByOrgUid(buildingDto);
        return response.success() ? response.getData() : emptyList();
    }

    private List<BuildingRoomDto> getBuildingRoomDtos(String tenantId){
        ListOrgBuildingRoomsReqDto roomsReqDto = new ListOrgBuildingRoomsReqDto();
        roomsReqDto.setOrgUid(tenantId);
        roomsReqDto.setAppKey(appCode);
        BaseResponse<DataPage<BuildingRoomDto>> response = buildingRoomApi.listOrgBuildingRooms(roomsReqDto);
        List<BuildingRoomDto> result = new ArrayList<>(20);
        if(response.success()){
            result.addAll(response.getData().getContent());
        }
        while (response.success() && response.getData().haveNextPage()){
            roomsReqDto.setPage(response.getData().nextPage().getPage());
            response = buildingRoomApi.listOrgBuildingRooms(roomsReqDto);
            result.addAll(response.getData().getContent());
        }
        return result;

    }




}
