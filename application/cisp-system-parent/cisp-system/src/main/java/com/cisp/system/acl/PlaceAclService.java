package com.cisp.system.acl;

import com.cisp.system.application.model.bo.PlaceBo;

import java.util.List;

public interface PlaceAclService {
    List<PlaceBo> getPlaceTree(String tenantId);

    List<PlaceBo> getUserManagedPlaces(String userId,String tenantId);

    List<PlaceBo> getPlaces(String tenantId,List<String> ids);


    List<PlaceBo> getPlaces(String tenantId,String parentId);

    PlaceBo getPlace(String tenantId,String id);
}
