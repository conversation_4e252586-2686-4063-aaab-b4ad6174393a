package com.cisp.system.api.service.user;


import com.cisp.system.api.annotation.BusiFuncBind;
import com.cisp.system.api.constant.BusiFuncMeta;
import com.cisp.system.api.constant.Constants;
import com.cisp.system.api.model.vo.PlaceTreeVo;
import com.cisp.system.api.model.vo.PlaceVo;
import com.seewo.kishframework.rest.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.List;

@Tag(name = "PlaceRestApi", description = "场地API接口")
@Path(Constants.PLACE_API_PREFIX)
@Produces({MediaType.APPLICATION_JSON})
public interface PlaceRestApi {
    /**
     * 获取场地列表
     * @return
     */
    @Operation(summary = "获取场地树")
    @GET
    @Path("/places")
    @Consumes({MediaType.APPLICATION_JSON})
    @BusiFuncBind(BusiFuncMeta.COMMON_AUTHENTICATION_FUNC)
    RestResponse<List<PlaceTreeVo>> getPlaceTree();

    /**
     * 获取当前用户所管理的场地列表
     * @return
     */
    @Operation(summary = "获取当前用户所管理的场地列表")
    @GET
    @Path("/managed-places")
    @Consumes({MediaType.APPLICATION_JSON})
    @BusiFuncBind(BusiFuncMeta.COMMON_AUTHENTICATION_FUNC)
    RestResponse<List<PlaceVo>> getUserManagedPlaces();


}
