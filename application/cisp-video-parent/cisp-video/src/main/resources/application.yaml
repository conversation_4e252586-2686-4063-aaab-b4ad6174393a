spring:
  aop:
    auto: true
    proxy-target-class: true

dubbo:
  application:
    register-mode: interface
    parameters:
      kishClusterGroup: ${kishClusterGroup:}
      kishCanary: ${kishCanary:}
      router: ${kishRouter:}
  protocols:
    dubbo:
      name: dubbo
      port: -1
    rest:
      name: rest
      port: 8080
      server: netty
  consumer:
    check: false
  registry:
    check: false
