package com.cisp.video.application.service;

import com.cisp.video.api.model.command.AiBoxResponse;
import com.cisp.video.api.model.command.AiBoxV2Response;
import org.jboss.resteasy.plugins.providers.multipart.MultipartFormDataInput;

public interface VideoAppService {
    void eventConvert(AiBoxResponse aiBoxResponse);

    void saveEventProof(MultipartFormDataInput formDataInput);

    void eventConvertV2(AiBoxV2Response aiBoxResponse);

    void saveEventProofV2(MultipartFormDataInput formDataInput);
}
