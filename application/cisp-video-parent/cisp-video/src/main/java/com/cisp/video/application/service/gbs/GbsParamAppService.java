package com.cisp.video.application.service.gbs;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cisp.infrastructure.config.SystemConfiguration;
import com.cisp.video.application.model.command.gbs.GbsParamBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;


@Service(value = "video.GbsParamAppService")
@Slf4j
public class GbsParamAppService implements InitializingBean {
    @Autowired
    private SystemConfiguration systemConfiguration;

    private Map<String, GbsParamBo> map;

    @Override
    public void afterPropertiesSet() throws Exception {
        String gbsParam = systemConfiguration.getGbsParam();
        map = JSONObject.parseObject(gbsParam, new TypeReference<Map<String, GbsParamBo>>() {
        });
    }

    public GbsParamBo getByPlatformCode(String platformCode) {
        return map.containsKey(platformCode) ? map.get(platformCode) : null;
    }

    public Set<String> getPlatformCodes() {
        return map.keySet();
    }
}
