package com.cisp.video.acl.impl;
import com.cisp.infrastructure.common.util.UUIDUtils;
import com.cisp.infrastructure.config.SystemConfiguration;
import com.cisp.video.acl.GbsAclService;
import com.cisp.video.acl.GbsApi;
import com.cisp.video.application.model.bo.StreamReqBo;
import com.cisp.video.application.model.command.gbs.*;
import com.cisp.video.application.service.gbs.GbsParamAppService;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;


@Slf4j
@Service(value = "video.gbsAclService")
public class GbsAclServiceImpl implements GbsAclService {
    @Autowired
    private GbsParamAppService gbsParamAppService;
    @Autowired
    private SystemConfiguration systemConfiguration;

    @DubboReference(protocol = "rest",
            parameters = {"kish.external.enabled", "true", "kish.external.ssl", "false"},
            url = "rest://${system.gbs.host}",
            timeout = 20 * 1000)
    private GbsApi gbsApi;

    private LoadingCache<String, GbsDomainInfoResp> domainCache = CacheBuilder.newBuilder()
            .initialCapacity(20)
            .build(new CacheLoader<String, GbsDomainInfoResp>() {
                @Override
                public GbsDomainInfoResp load(String s) throws Exception {
                    return getDomainInfo(s);
                }
            });



    @Override
    public StreamStartResp getRecordFileStream(StreamReqBo streamReqBo) {
        GbsRequestHeader header = getByPlatformCode(streamReqBo.getPlatformCode());
        GbsRequest<GbsRecordFileReq> request = buildGbsRecordReq(streamReqBo, header);
        try {
            GbsRestResponse<StreamStartResp> response = gbsApi.getRecordFileSteam(header.getDomain(), header.getTimestamp(), header.getSignature(), request);
            return response.isSuccess() ? response.getData() : null;
        } catch (Exception e) {
            log.error("gbsApiService invoke error {}", e.getMessage());
            return null;
        }
    }

    @Override
    public StreamStartResp getLiveStream(StreamReqBo streamReqBo) {
        Integer idleTime = Objects.isNull(streamReqBo.getIdleTime()) ? systemConfiguration.getGbsIdleTime() : streamReqBo.getIdleTime();
        GbsRequestHeader header = getByPlatformCode(streamReqBo.getPlatformCode());
        StreamOpReq opReq = StreamOpReq.builder().deviceId(streamReqBo.getDeviceId().split(":")[0])
                .idleTime(idleTime)
                .useUdp(systemConfiguration.getGbsUseUdp()).build();
        GbsRequest<StreamOpReq> request = GbsRequest.<StreamOpReq>builder()
                .traceId(UUIDUtils.generateUUID())
                .time(header.getTimestamp())
                .param(opReq)
                .build();
        try {
            GbsRestResponse<StreamStartResp> response = gbsApi.startStream(header.getDomain(), header.getTimestamp(), header.getSignature(), request);
            return response.isSuccess() ? response.getData() : null;
        } catch (Exception e) {
            log.error("gbsApi invoke error {}", e.getMessage());
            return null;
        }
    }
    @Override
    public List<GbsDeviceResp> getDeviceList(String platformCode) {
        try {
            GbsParamBo gbsParamVo = gbsParamAppService.getByPlatformCode(platformCode);
            GbsRequestHeader header = new GbsRequestHeader(gbsParamVo.getDomain(), gbsParamVo.getSecret());
            if (StringUtils.isNotBlank(gbsParamVo.getDeviceId())) {
                GbsRestResponse<GbsDeviceResp> response = gbsApi.getDeviceList(header.getDomain(), header.getTimestamp(), header.getSignature(), gbsParamVo.getDeviceId());
                if (!response.isSuccess()) {
                    log.warn("GBS服务获取设备信息列表出错: {},平台Id为：{}", response.getMessage(), gbsParamVo.getDeviceId());
                }
                return response.isSuccess() ? Arrays.asList(response.getData()) : null;
            }
            GbsRestResponse<GbsDeviceBaseResp> devices = gbsApi.getAllDeviceByDomain(header.getDomain(), header.getTimestamp(), header.getSignature());
            if (!devices.isSuccess()) {
                log.warn("getAllDeviceByDomain failure: {}, domain {}", devices.getMessage(), header.getDomain());
            }
            return devices.isSuccess() ? devices.getData().getDeviceList() : null;

        } catch (Exception e) {
            log.error("GBS服务异常：{}", e.getMessage());
        }
        return null;
    }

    @Override
    public List<GbsDeviceResp> getDeviceList(String domain, String secret) {
        List<GbsDeviceResp> list = Collections.emptyList();
        GbsRequestHeader header = new GbsRequestHeader(domain, secret);
        try {
            GbsRestResponse<GbsDeviceBaseResp> devices = gbsApi.getAllDeviceByDomain(header.getDomain(), header.getTimestamp(), header.getSignature());
            if (!devices.isSuccess()) {
                log.warn("getDeviceList failure: {}, domain {}", devices.getMessage(), header.getDomain());
            } else {
                list = devices.getData().getDeviceList();
            }
        } catch (Exception e) {
            log.error("GBS服务异常：{}", e.getMessage());
        }

        return list;
    }

    @Override
    public List<GbsDomainInfoResp> getAllDomain() {
        List<GbsDomainInfoResp> list = Collections.emptyList();
        try {
            GbsRestResponse<List<GbsDomainInfoResp>> response = gbsApi.getDomains(null);
            if (response.isSuccess()) {
                list = response.getData();
            } else {
                log.error("GBS服务异常：{}", response.getMessage());
            }
        } catch (Exception e) {
            log.error("GBS服务异常：{}", e.getMessage());
        }
        return list;
    }



    private GbsDomainInfoResp getDomainInfo(String domain) {
        GbsRestResponse<List<GbsDomainInfoResp>> domains = gbsApi.getDomains(domain);
        if (domains.isSuccess()) {
            List<GbsDomainInfoResp> data = domains.getData();
            if (CollectionUtils.isNotEmpty(data)) {
                return data.get(0);
            }
        }
        return null;
    }



    private GbsRequestHeader getByPlatformCode(String platformCode) {
        GbsParamBo gbsParamVo = gbsParamAppService.getByPlatformCode(platformCode);
        String domain = "";
        String secret = "";
        if (Objects.nonNull(gbsParamVo)) {
            domain = gbsParamVo.getDomain();
            secret = gbsParamVo.getSecret();
        } else {
            try {
                GbsDomainInfoResp gbsDomainInfoResp = domainCache.get(platformCode);
                if (Objects.nonNull(gbsDomainInfoResp)) {
                    domain = gbsDomainInfoResp.getDomain();
                    secret = gbsDomainInfoResp.getSecret();
                }
            } catch (ExecutionException e) {
                log.error("domainCache get domainInfo error", e);
            }
        }
        GbsRequestHeader header = new GbsRequestHeader(domain, secret);
        return header;
    }


    private GbsRequest<GbsRecordFileReq> buildGbsRecordReq(StreamReqBo streamReqBo, GbsRequestHeader header) {
        GbsRecordFileReq gbsRecordFileReq = GbsRecordFileReq.builder()
                .deviceId(streamReqBo.getDeviceId().split(":")[0])
                .startTime(streamReqBo.getStartTime())
                .endTime(streamReqBo.getEndTime())
                .idleTime(streamReqBo.getIdleTime())
                .useUdp(systemConfiguration.getGbsUseUdp())
                .build();
        GbsRequest<GbsRecordFileReq> request = GbsRequest.<GbsRecordFileReq>builder()
                .traceId(UUIDUtils.generateUUID())
                .time(header.getTimestamp())
                .param(gbsRecordFileReq)
                .build();
        return request;
    }

    @Data
    @NoArgsConstructor
    class GbsRequestHeader {
        private Long timestamp;
        private String domain;
        // private String signature;
        private String secret;

        public GbsRequestHeader(String domain, String secret) {
            this.domain = domain;
            this.timestamp = 1L;
            // this.signature = "123456";
            this.secret = secret;
        }

        private String getSignature() {
            // Long timestamp = 1L;
            String hashStr = this.domain + ":" + this.timestamp + ":" + this.secret;
            String signature = DigestUtils.md5DigestAsHex(hashStr.getBytes());
            //String signature = "123456";
            return signature;
        }
    }
}
