package com.cisp.access.model.command;

import com.cisp.access.api.constant.RuleStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NonNull;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class AccessRulesCreateDTO {

    /**
     * 租户id
     */
    private String tenantId;


    /**
     * 规则名称
     */
    @NotEmpty
    @Schema(description = "规则名称")
    private String name;
    
    /**
     * 规则类型: normal(常规)/temp(临时)
     */
    @Schema(description = "规则类型")
    private String type;
    
    /**
     * 规则状态: enable(启用)/disable(禁用)
     */
    @Schema(description = "规则状态")
    private String status= RuleStatusEnum.DISABLE.getCode();
    
    /**
     * 是否跳过节假日
     */
    private Boolean skipHoliday=false;
    
    /**
     * 通行方向: in(进入)/out(离开)
     */
    @NotEmpty
    @Schema(description = "通行方向")
    private String direction;
    
    /**
     * 适用对象配置(组织结构维度)
     */
    @NotEmpty
    @Schema(description = "通行对象")
    private List<ApplicableObject> applicableObjects;
    
    /**
     * 时间配置规则(可配置多个时间段)
     */
    @NotEmpty
    @Schema(description = "通行时间")
    private List<TimeSetting> timeSettings;
    
    /**
     * 适用目标配置(场地/设备维度)
     */
    @Schema(description = "通行场地/设备")
    private List<ApplicableTarget> applicableTargets;

}

