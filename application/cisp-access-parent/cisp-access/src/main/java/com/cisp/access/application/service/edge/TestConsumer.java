package com.cisp.access.application.service.edge;

import com.cisp.access.api.constant.Constants;
import com.cisp.access.api.model.command.RuleDeployDto;
import com.cisp.access.application.service.strategy.RuleDeployStrategy;
import com.cisp.access.model.vo.AccessRulesVO;
import com.cisp.device.api.model.notify.DeviceNotifyDto;
import com.cisp.device.api.model.notify.UpdateDeviceDto;
import com.cisp.gateway.api.model.ApiEventDto;
import com.cisp.gateway.api.model.HttpReqDto;
import com.cisp.gateway.api.model.InvocationModelEnum;
import com.cisp.gateway.api.model.InvocationTypeEnum;
import com.cisp.gateway.sdk.annotation.EventListener;
import com.cisp.infrastructure.common.util.Jsons;
import com.cisp.infrastructure.common.util.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service
public class TestConsumer {

//    @EventListener(topic = "cispAccessMgt", tag = "deviceUpdate", classes = DeviceNotifyDto.class)
    public void handle(DeviceNotifyDto deployDto) {
        log.info("handle deviceUpdate event: {}", Jsons.toJson(deployDto));
    }

}
