package com.cisp.access.domain.service.impl;

import com.cisp.access.domain.entity.RuleDeployRecordEntity;
import com.cisp.access.domain.repository.RuleDeployRecordRepository;
import com.cisp.access.domain.service.RuleDeployRecordDomainService;
import com.seewo.kishframework.idworker.IdWorkers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class RuleDeployRecordDomainServiceImpl implements RuleDeployRecordDomainService {

    @Autowired
    private  RuleDeployRecordRepository ruleDeployRecordRepository;



    @Override
    public void saveRuleDeployRecord(List<RuleDeployRecordEntity> entities) {

        entities.forEach(entity -> {
            if (Objects.isNull(entity.getId())) {
                entity.setId(IdWorkers.idWorker().nextId());
            }
            ruleDeployRecordRepository.fillCreated(entity);
        });
        ruleDeployRecordRepository.insertListAllFiled(entities);
    }


    @Override
    public void create(RuleDeployRecordEntity entity) {
        ruleDeployRecordRepository.insertSelective(entity);
    }

    @Override
    public List<RuleDeployRecordEntity> getByRuleId(Long ruleId) {
        return ruleDeployRecordRepository.getByRuleId(ruleId);
    }

    @Override
    public void updateStatus(Long ruleId) {
        ruleDeployRecordRepository.updateStatus(ruleId);
    }

    @Override
    public List<RuleDeployRecordEntity> getByDeviceRecordId(Long deviceRecordId,Long ruleId) {
        return ruleDeployRecordRepository.getByDeviceRecordId(deviceRecordId,ruleId);
    }

    @Override
    public List<RuleDeployRecordEntity> getByPersonId(Long personId) {
        return ruleDeployRecordRepository.getByPersonId(personId);
    }
}