package com.cisp.access.api.service.impl.user;

import com.cisp.access.api.model.query.AccessRecordQuery;
import com.cisp.access.api.service.user.AccessRecordRestApi;
import com.cisp.access.application.model.query.AccessRecordQueryBuilder;
import com.cisp.access.application.service.AccessRecordAppService;
import com.cisp.access.model.command.AccessRecordsCreateDTO;
import com.cisp.access.model.vo.AccessRecordsVO;
import com.cisp.infrastructure.common.context.ContextUtils;
import com.cisp.infrastructure.common.query.AbsQueryBuilder;
import com.seewo.kishframework.page.Page;
import com.seewo.kishframework.rest.RestResponse;
import com.seewo.kishframework.rest.VoidRestResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.ws.rs.BeanParam;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Response;
import java.io.File;
import java.nio.charset.StandardCharsets;

@Slf4j
@DubboService(protocol = "rest", register = false, validation = "true")
@Service("access.AccessRecordRestApiImpl")
public class AccessRecordRestApiImpl implements AccessRecordRestApi {

    @Autowired
    private AccessRecordAppService accessRecordAppService;

    @Override
    public RestResponse<Page<AccessRecordsVO>> queryPage(
            @QueryParam("page") Integer page,
            @QueryParam("size") Integer size, @BeanParam AccessRecordQuery query) {
        query.setTenantId(ContextUtils.get(ContextUtils.TENANT_KEY));
        AbsQueryBuilder queryBuilder = new AccessRecordQueryBuilder().setQuery(query)
                .setCurrentPage(page).setPageSize(size);
        return RestResponse.success(accessRecordAppService.queryPage((AccessRecordQueryBuilder) queryBuilder));
    }

    @Override
    public Response exportAccessRecords(@QueryParam("page") Integer page,
                                         @QueryParam("size") Integer size,@BeanParam AccessRecordQuery query) {

        String filePath = "通行记录.xlsx";
        AbsQueryBuilder queryBuilder = new AccessRecordQueryBuilder().setQuery(query)
                .setCurrentPage(page).setPageSize(size);
        query.setTenantId(ContextUtils.getTenantId());
        accessRecordAppService.exportToExcel((AccessRecordQueryBuilder) queryBuilder,filePath);
        File file = new File(filePath);
        Response.ResponseBuilder response = Response.ok(file);
        response.header("Content-Disposition",
                "attachment; filename=" + new String(filePath.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
        return response.build();
    }

    @Override
    public VoidRestResponse reportAccessRecord(AccessRecordsCreateDTO dto) {
        //TODO mq削峰
        dto.setTenantId(ContextUtils.get(ContextUtils.TENANT_KEY));
        accessRecordAppService.reportAccessRecord(dto);
        return VoidRestResponse.success();
    }
}