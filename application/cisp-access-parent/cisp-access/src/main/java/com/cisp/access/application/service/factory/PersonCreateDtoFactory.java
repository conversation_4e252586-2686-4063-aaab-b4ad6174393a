package com.cisp.access.application.service.factory;

import com.cisp.access.api.constant.SyncStatusEnum;
import com.cisp.access.api.model.command.RuleDeployDto;
import com.cisp.access.domain.entity.UserFaceEntity;
import com.cisp.access.domain.service.UserFaceDomainService;
import com.cisp.access.infrastructure.util.ImageUtil;
import com.cisp.access.model.command.ApplicableObject;
import com.cisp.access.model.vo.AccessRulesVO;
import com.cisp.device.api.model.agent.acs.peoplelib.PeopleCreateDto;
import com.cisp.device.api.model.agent.acs.peoplelib.PeopleImageDto;
import com.cisp.device.api.model.agent.acs.peoplelib.TimeTemplateRangeDto;
import com.cisp.infrastructure.config.SystemConfiguration;
import com.seewo.kishframework.spring.context.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@Service
public class PersonCreateDtoFactory {

    private static final ExecutorService executorService = Executors.newFixedThreadPool(3);



    public static List<PeopleCreateDto> createPersonUpdateDto(RuleDeployDto deployDto, AccessRulesVO accessRulesVO, Integer lastId, boolean isTemp, Map<String, Long> userIdToPersonIdMap) {
        List<ApplicableObject> applicableObjects = accessRulesVO.getApplicableObjects();
        if (applicableObjects == null || applicableObjects.isEmpty()) {
            return new ArrayList<>();
        }
        ApplicationContext applicationContext = SpringContextUtils.applicationContext();
        UserFaceDomainService userFaceDomainService = applicationContext.getBean(UserFaceDomainService.class);

        List<PeopleCreateDto> peopleCreateDtos = new ArrayList<>();
        for (ApplicableObject applicableObject : applicableObjects) {
            PeopleCreateDto peopleCreateDto = new PeopleCreateDto();
            String userId = applicableObject.getId();
            UserFaceEntity dbEntity = userFaceDomainService.getUserByUserId(userId);

            Long personId = userIdToPersonIdMap.get(userId);
            if(Objects.isNull(personId)){
                log.error("userId {} dose not have record about user face",userId);

                UserFaceEntity userFace = new UserFaceEntity();
                userFace.setUserName(applicableObject.getName());
                userFace.setUserId(applicableObject.getId());

                userFaceDomainService.create(userFace);

                UserFaceEntity newEntity =  userFaceDomainService.getUserByUserId(userId);

                peopleCreateDto.setPersonId(newEntity.getPersonId());
                peopleCreateDto.setPersonName(applicableObject.getName());
                peopleCreateDto.setStatus(SyncStatusEnum.FAILED.getCode());
                peopleCreateDtos.add(peopleCreateDto);
                continue;
            }
            if(Objects.nonNull(dbEntity) && Objects.isNull(dbEntity.getImageUrl())){
                log.error("userId {} dose not have imageUrl",userId);
                continue;
            }
            peopleCreateDto.setPersonId(personId);
            peopleCreateDto.setPersonName(applicableObject.getName());
            TimeTemplateRangeDto timeTemplateRangeDto = new TimeTemplateRangeDto();
            timeTemplateRangeDto.setBeginTime(0L);
            timeTemplateRangeDto.setEndTime(4294967295L);
            timeTemplateRangeDto.setIndex(lastId);
            peopleCreateDto.setTimeTemplateList(Collections.singletonList(timeTemplateRangeDto));

            List<PeopleImageDto> imageList = new ArrayList<>();
            PeopleImageDto imageDto = new PeopleImageDto();
            imageDto.setFaceId(personId);
            imageDto.setName(applicableObject.getName() + ".jpg");

            SystemConfiguration systemConfiguration =  applicationContext.getBean(SystemConfiguration.class);
            String filePath = systemConfiguration.getRootPath() + File.separator + "userFace" + File.separator + applicableObject.getId() + ".jpg";
            setImage(imageDto, filePath);
            imageList.add(imageDto);
            peopleCreateDto.setImageList(imageList);
            peopleCreateDtos.add(peopleCreateDto);
        }
        return peopleCreateDtos;
    }

    private static void setImage(PeopleImageDto imageDto, String filePath) {
        String base64;
        try {
            base64 = ImageUtil.convertImageToBase64(filePath);
            imageDto.setData(base64);
            imageDto.setSize(base64.length());
        } catch (IOException e) {
            log.error("获取图片异常{},{}",e.getMessage(),filePath);
        }
    }

    public static List<PeopleCreateDto> createPersonCreateDto(List<UserFaceEntity> userFaceEntities) {
        List<PeopleCreateDto> peopleCreateDtos = new ArrayList<>();
        for (UserFaceEntity userFaceEntity : userFaceEntities) {
            PeopleCreateDto peopleCreateDto = new PeopleCreateDto();
            Long personId = userFaceEntity.getPersonId();
            peopleCreateDto.setPersonId(personId);
            peopleCreateDto.setPersonName(userFaceEntity.getUserName());
            TimeTemplateRangeDto timeTemplateRangeDto = new TimeTemplateRangeDto();
     /*       timeTemplateRangeDto.setBeginTime(0L);
            timeTemplateRangeDto.setEndTime(4294967295L);*/
            peopleCreateDto.setTimeTemplateList(Collections.singletonList(timeTemplateRangeDto));
            List<PeopleImageDto> imageList = new ArrayList<>();
            PeopleImageDto imageDto = new PeopleImageDto();
            imageDto.setFaceId(personId);
            imageDto.setName(userFaceEntity.getUserName() + ".jpg");

            ApplicationContext applicationContext = SpringContextUtils.applicationContext();
            SystemConfiguration systemConfiguration =  applicationContext.getBean(SystemConfiguration.class);
            String filePath = systemConfiguration.getRootPath() + File.separator + "userFace" + File.separator + userFaceEntity.getUserId() + ".jpg";
            setImage(imageDto, filePath);
            imageList.add(imageDto);
            peopleCreateDto.setImageList(imageList);
            peopleCreateDtos.add(peopleCreateDto);
        }
        return peopleCreateDtos;
    }



    // 添加一个关闭线程池的方法，在应用关闭时调用
    public static void shutdownExecutorService() {
        executorService.shutdown();
    }
}