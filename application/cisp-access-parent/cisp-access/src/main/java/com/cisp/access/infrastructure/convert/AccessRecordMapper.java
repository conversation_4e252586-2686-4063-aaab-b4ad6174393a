package com.cisp.access.infrastructure.convert;

import com.cisp.access.domain.entity.AccessRecordsEntity;
import com.cisp.access.model.command.AccessRecordsCreateDTO;
import com.cisp.access.model.vo.AccessRecordsVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring", uses = {AccessRecordCommonMapper.class})
public interface AccessRecordMapper {

    AccessRecordsVO toVo(AccessRecordsEntity accessRecordEntity);

    List<AccessRecordsVO> toVo(List<AccessRecordsEntity> entities);

    AccessRecordsEntity from(AccessRecordsCreateDTO accessRecordCreateDTO);

    List<AccessRecordsEntity> from(List<AccessRecordsCreateDTO> accessRecordCreateDTOs);
}