package com.cisp.access.application.service.impl;

import com.cisp.access.api.constant.AccessTypeEnum;
import com.cisp.access.api.constant.DirectionEnum;
import com.cisp.access.api.constant.UserTypeEnum;
import com.cisp.access.application.model.query.AccessRecordQueryBuilder;
import com.cisp.access.application.service.AccessRecordAppService;
import com.cisp.access.domain.entity.AccessRecordsEntity;
import com.cisp.access.domain.service.AccessRecordDomainService;
import com.cisp.access.infrastructure.convert.AccessRecordMapper;
import com.cisp.access.model.command.AccessRecordsCreateDTO;
import com.cisp.access.model.vo.AccessRecordsVO;
import com.cisp.device.api.model.vo.DeviceVo;
import com.cisp.device.api.service.DeviceApiService;
import com.cisp.identity.api.constant.NodeTypeEnum;
import com.cisp.identity.api.model.dto.StudentDto;
import com.cisp.identity.api.model.dto.TeacherDto;
import com.cisp.identity.api.service.BasicInfoApiService;
import com.cisp.infrastructure.common.util.TimeUtils;
import com.cisp.infrastructure.cstore.CstoreClient;
import com.seewo.kishframework.exception.PlatformExceptionCode;
import com.seewo.kishframework.page.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AccessRecordAppServiceImpl implements AccessRecordAppService {

    @Autowired
    private AccessRecordDomainService accessRecordDomainService;

    @Autowired
    private DeviceApiService deviceApiService;

    @DubboReference
    private BasicInfoApiService basicInfoApiService;

    @Autowired
    private AccessRecordMapper mapper;

    @Lazy
    @Resource(name = "resource-client")
    private CstoreClient cstoreClient;

    @Override
    public Page<AccessRecordsVO> queryPage(AccessRecordQueryBuilder queryBuilder) {
        Page<AccessRecordsEntity> entityPage = accessRecordDomainService.queryPage(queryBuilder);
        List<String> fileKeys = entityPage.getContent().stream().map(AccessRecordsEntity::getSnapshotUrl).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(fileKeys)) {
            Map<String, String> urlMap = cstoreClient.getDownloadUrls(fileKeys);
            entityPage.getContent().forEach(record -> {
                String originalUrl = record.getSnapshotUrl();
                if (urlMap.containsKey(originalUrl)) {
                    record.setSnapshotUrl(urlMap.get(originalUrl));
                }
            });
        }
        return entityPage.map(r -> mapper.toVo(r));
    }

    @Override
    public void exportToExcel(AccessRecordQueryBuilder queryBuilder, String filePath) {
        Page<AccessRecordsEntity> entityPage = accessRecordDomainService.queryPage(queryBuilder);
        List<AccessRecordsEntity> fieldInfos = entityPage.getContent();
        log.info("export access records");
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("通行记录");
        LinkedList<String> head = new LinkedList<>();
        head.add("姓名");
        head.add("人员类型");
        head.add("学号/手机号");
        head.add("班级/部门");
        head.add("通行时间");
        head.add("通行方式");
        head.add("场地");
        head.add("方向");
        head.add("设备名称");
        head.add("识别照片");
        head.add("档案照片");
        // 设置列宽度自适应
        for (int i = 0; i < head.size(); i++) {
            sheet.autoSizeColumn(i);
        }

        // 设置行高为30
        sheet.setDefaultRowHeight((short) 1500);

        // 写入表头
        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < head.size(); i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(head.get(i));
        }

        if(CollectionUtils.isEmpty(fieldInfos)){
            return;
        }
        List<String> fileKeys = entityPage.getContent().stream().map(AccessRecordsEntity::getSnapshotUrl).collect(Collectors.toList());
        Map<String, String> urlMap = cstoreClient.getDownloadUrls(fileKeys);

        // 写入数据
        for (int i = 0; i < fieldInfos.size(); i++) {
            AccessRecordsEntity record = fieldInfos.get(i);
            Row row = sheet.createRow(i + 1);
            for (int j = 0; j < head.size(); j++) {
                String co = head.get(j);
                Cell cell = row.createCell(j);
                switch (co) {
                    case "姓名":
                        cell.setCellValue(record.getUserName());
                        break;
                    case "人员类型":
                        cell.setCellValue(UserTypeEnum.getByCode(record.getUserType()));
                        break;
                    case "学号/手机号":
                        cell.setCellValue(UserTypeEnum.STUDENT.getCode().equals(record.getUserType()) ? record.getStudentCode() : record.getPhoneNum());
                        break;
                    case "班级/部门":
                        cell.setCellValue(record.getOrgName());
                        break;
                    case "通行时间":
                        cell.setCellValue(TimeUtils.timestampToDateTimeString(record.getRecordTime()));
                        break;
                    case "通行方式":
                        cell.setCellValue(AccessTypeEnum.getByCode(record.getAccessType()));
                        break;
                    case "场地":
                        cell.setCellValue(record.getPlaceName());
                        break;
                    case "方向":
                        cell.setCellValue(DirectionEnum.getByCode(record.getDirection()));
                        break;
                    case "设备名称":
                        cell.setCellValue(record.getDeviceName());
                        break;
                    case "识别照片":
                        setPictureToCell(workbook, sheet, i, j, urlMap.get(record.getSnapshotUrl()));
                        break;
                    case "档案照片":
                        setPictureToCell(workbook, sheet, i, j, record.getImgUrl());
                        break;
                    default:
                        break;
                }
            }
        }
        // 保存Excel文件
        try (FileOutputStream fileOut = new FileOutputStream(filePath)) {
            workbook.write(fileOut);
            log.info("excel build success");
        } catch (IOException e) {
            log.info(e.getMessage());
        }

    }

    private void setPictureToCell(Workbook workbook, Sheet sheet, int i, int j, String facePhotoUrl) {
        try {
            byte[] imageBytes = downloadImage(facePhotoUrl);
            if (imageBytes != null) {
                // 压缩和调整图片尺寸
                BufferedImage image = resizeImage(imageBytes, 50, 50);
                // 将调整后的图片转换为字节数组
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(image, "jpeg", baos);
                byte[] resizedImageBytes = baos.toByteArray();
                // 将图片写入Excel
                int pictureIdx = workbook.addPicture(resizedImageBytes, Workbook.PICTURE_TYPE_JPEG);
                Drawing<?> drawing = sheet.createDrawingPatriarch();
                CreationHelper helper = workbook.getCreationHelper();
                ClientAnchor anchor = helper.createClientAnchor();
                anchor.setCol1(j); // 图片插入的列索引
                anchor.setRow1(i + 1); // 图片插入的行索引
                anchor.setCol2(j + 1); // 图片结束的列索引
                anchor.setRow2(i + 2); // 图片结束的行索引

                Picture picture = drawing.createPicture(anchor, pictureIdx);
                picture.resize();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private byte[] downloadImage(String urlString) throws IOException {
        URL url = new URL(urlString);
        try (InputStream inputStream = url.openStream()) {
            return IOUtils.toByteArray(inputStream);
        }
    }

    private BufferedImage resizeImage(byte[] imageBytes, int width, int height) throws IOException {
        InputStream inputStream = new ByteArrayInputStream(imageBytes);
        BufferedImage image = ImageIO.read(inputStream);

        double scaleX = (double) width / image.getWidth();
        double scaleY = (double) height / image.getHeight();
        double scale = Math.max(scaleX, scaleY);

        int newWidth = (int) (image.getWidth() * scale);
        int newHeight = (int) (image.getHeight() * scale);

        BufferedImage resizedImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = resizedImage.createGraphics();
        g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g.drawImage(image, 0, 0, newWidth, newHeight, null);
        g.dispose();

        return resizedImage;
    }

    @Override
    public void reportAccessRecord(AccessRecordsCreateDTO dto) {
        String tenantId = dto.getTenantId();
        List<String> userIds = Collections.singletonList(dto.getUserId());
        DeviceVo deviceVo = deviceApiService.getByDeviceId(tenantId, dto.getDeviceId());
        List<StudentDto> studentDtos = basicInfoApiService.getByStudentIds(tenantId, userIds);

        if (CollectionUtils.isNotEmpty(studentDtos)) {
            StudentDto studentDto = studentDtos.get(0);
            dto.setOrgId(studentDto.getClassId());
            dto.setOrgType(NodeTypeEnum.CLASS.getCode());
            dto.setStudentCode(studentDto.getStudentCode());
            dto.setUserType(NodeTypeEnum.STUDENT.getCode());
            dto.setOrgName(studentDto.getClassName());
        } else {
            List<TeacherDto> teacherDtos = basicInfoApiService.getTeacherByIds(tenantId, userIds);
            if (CollectionUtils.isNotEmpty(teacherDtos)) {
                TeacherDto teacherDto = teacherDtos.get(0);
                List<TeacherDto.Dept> deptList = teacherDto.getDeptList();
                if (CollectionUtils.isNotEmpty(deptList)) {
                    dto.setOrgId(deptList.get(0).getId());
                    dto.setOrgName(deptList.get(0).getName());
                }
                dto.setOrgType(NodeTypeEnum.DEPARTMENT.getCode());
                dto.setUserType(NodeTypeEnum.TEACHER.getCode());
                dto.setPhoneNum(teacherDto.getPhone());

            }
        }
        dto.setDeviceName(deviceVo.getName());
        dto.setDeviceId(deviceVo.getDeviceId());
        dto.setDirection(deviceVo.getDirection());
        dto.setPlaceId(deviceVo.getPlaceId());
        dto.setPlaceName(deviceVo.getPlaceName());
        dto.setAccessType(AccessTypeEnum.RULE_PASSAGE.getCode());
        accessRecordDomainService.reportAccessRecord(mapper.from(dto));
    }
}