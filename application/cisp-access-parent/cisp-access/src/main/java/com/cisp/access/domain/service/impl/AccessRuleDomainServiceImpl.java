package com.cisp.access.domain.service.impl;

import com.cisp.access.application.model.query.AccessRulesQueryBuilder;
import com.cisp.access.domain.entity.AccessRulesEntity;
import com.cisp.access.domain.repository.AccessRulesRepository;
import com.cisp.access.domain.service.AccessRulesDomainService;
import com.cisp.access.model.vo.AccessRulesVO;
import com.seewo.kishframework.idworker.IdWorkers;
import com.seewo.kishframework.page.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.Objects;

@Service
public class AccessRuleDomainServiceImpl implements AccessRulesDomainService {


    @Autowired
    private AccessRulesRepository accessRuleRepository;

    @Override
    public AccessRulesEntity createRule(AccessRulesEntity entity) {
//        accessRuleRepository.fillCreated(entity);
        entity.setId(IdWorkers.idWorker().nextId());
        accessRuleRepository.insert(entity);
        return entity;

    }

    @Override
    public AccessRulesEntity updateRule(Long id, AccessRulesEntity entity) {
        accessRuleRepository.fillUpdated(entity);
        Example example = new Example(AccessRulesEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", id);
        accessRuleRepository.updateByExampleSelective(entity, example);
        return entity;
    }

    @Override
    public void deleteRule(Long id) {
        accessRuleRepository.deleteByPrimaryKey(id);
    }

    @Override
    public Page<AccessRulesEntity> queryPage(AccessRulesQueryBuilder query) {
        return accessRuleRepository.queryPage(
                query.getTenantId(),
                query.getType(),
                query.getName(),
                query.getPageRequest());
    }

    @Override
    public void updateRuleStatus(Long id, String status) {
        accessRuleRepository.updateStatus(id, status);
    }

    @Override
    public AccessRulesEntity getRuleById(Long ruleId) {
        AccessRulesEntity entity = new AccessRulesEntity();
        entity.setId(ruleId);
        return accessRuleRepository.selectOne(entity);
    }

    @Override
    public AccessRulesEntity findByName(String name) {
        Example example = new Example(AccessRulesEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("name", name);
        return accessRuleRepository.selectOneByExample(example);
    }

    @Override
    public List<AccessRulesEntity> getByPlaceIdAndDirection(String placeId, String tenantId, String direction) {
        Example example = new Example(AccessRulesEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("tenantId", tenantId);
        if(Objects.nonNull(direction)){
            criteria.andEqualTo("direction", direction);
        }
        criteria.andLike("applicableTargets", "%" + placeId + "%");
        return accessRuleRepository.selectByExample(example);
    }

    @Override
    public List<AccessRulesEntity> getByClassId(String classId, String tenantId) {
        Example example = new Example(AccessRulesEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("tenantId", tenantId);
        criteria.andLike("applicableObjects", "%" + classId + "%");
        return accessRuleRepository.selectByExample(example);
    }
}