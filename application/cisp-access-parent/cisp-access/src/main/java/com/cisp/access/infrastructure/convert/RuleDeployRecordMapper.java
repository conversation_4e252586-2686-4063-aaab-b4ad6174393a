package com.cisp.access.infrastructure.convert;

import com.cisp.access.domain.entity.RuleDeployRecordEntity;
import com.cisp.access.domain.entity.RuleSyncLogEntity;
import com.cisp.access.model.command.RuleDeployRecordCreateDTO;
import com.cisp.access.model.vo.RuleSyncLogVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface RuleDeployRecordMapper {

    RuleDeployRecordEntity from(RuleDeployRecordCreateDTO dto);


    RuleDeployRecordCreateDTO to(RuleDeployRecordEntity entity);
}