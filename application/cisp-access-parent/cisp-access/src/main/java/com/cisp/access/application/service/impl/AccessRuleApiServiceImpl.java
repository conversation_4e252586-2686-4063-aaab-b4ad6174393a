package com.cisp.access.application.service.impl;

import com.cisp.access.api.service.admin.AccessRuleApiService;
import com.cisp.access.application.service.AccessRuleAppService;
import com.cisp.access.model.vo.AccessRulesVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class AccessRuleApiServiceImpl implements AccessRuleApiService {

    @Autowired
    private AccessRuleAppService accessRuleAppService;
    @Override
    public List<AccessRulesVO> getByPlaceIdAndDirection(String placeId, String tenantId, String direction) {
        return accessRuleAppService.getByPlaceIdAndDirection(placeId,tenantId,direction);
    }
}
