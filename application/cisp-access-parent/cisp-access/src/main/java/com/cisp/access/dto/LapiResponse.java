package com.cisp.access.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class LapiResponse {
    @JsonProperty("ResponseURL")
    private String responseURL;
    @JsonProperty("CreatedID")
    private int createdID;
    @JsonProperty("ResponseCode")
    private int responseCode;
    @JsonProperty("SubResponseCode")
    private int subResponseCode;
    @JsonProperty("ResponseString")
    private String responseString;
    @JsonProperty("StatusCode")
    private int statusCode;
    @JsonProperty("StatusString")
    private String statusString;
    @JsonProperty("Data")
    private LapiCommonData data;

}