package com.cisp.access.dto;

import com.dsa.pts.application.model.timetemplate.UTimeTemplateBriefDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
@Data
public class LapiCommonData {
    @JsonProperty("Num")
    private Integer num;
    @JsonProperty("List")
    private List<UTimeTemplateBriefDto> list;
    @JsonProperty("ID")
    private Integer id;




}