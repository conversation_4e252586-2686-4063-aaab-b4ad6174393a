<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cisp.access.domain.repository.RuleSyncLogRepository">
  <resultMap id="BaseResultMap" type="com.cisp.access.domain.entity.RuleSyncLogEntity">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="rule_id" jdbcType="BIGINT" property="ruleId" />
    <result column="device_id" jdbcType="VARCHAR" property="deviceId" />
    <result column="device_record_id" jdbcType="BIGINT" property="deviceRecordId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_at" jdbcType="BIGINT" property="createdAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_at" jdbcType="BIGINT" property="updatedAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="failed_reason" jdbcType="LONGVARCHAR" property="failedReason" />
  </resultMap>

  <!-- 根据规则 ID 列表查询规则同步日志列表 -->
  <select id="queryListByRuleId" resultMap="BaseResultMap">
    SELECT * FROM rule_sync_log
    WHERE rule_id IN
    <foreach collection="ruleIds" item="ruleId" open="(" separator="," close=")">
      #{ruleId}
    </foreach>
  </select>

    <update id="updateSyncLog">
        UPDATE rule_sync_log
        SET status = #{status},
            failed_reason = #{failedReason}
        WHERE rule_id = #{ruleId}
          AND device_record_id = #{deviceId}
    </update>

  <select id="delByDeviceRecordIds" >
    delete  FROM rule_sync_log
    WHERE device_record_id IN
    <foreach collection="deviceRecordIds" item="deviceRecordId" open="(" separator="," close=")">
      #{deviceRecordId}
    </foreach>
    </select>
</mapper>