# \u6570\u636E\u5E93\u914D\u7F6E
jdbc.driverClass = com.mysql.cj.jdbc.Driver
jdbc.url = ********************************************************************************************************************************
jdbc.user = seewo_cisp_server
jdbc.password = Uu.78Fsv


# java\u63A5\u53E3\u548C\u5B9E\u4F53\u7C7B
targetJavaProject = /src/main/java
targetMapperPackage = com.cisp.access.domain.repository
targetModelPackage = com.cisp.access.domain.entity
# XML\u751F\u6210\u8DEF\u5F84
targetResourcesProject = /src/main/resources
targetXMLPackage = mapper

#c3p0
jdbc.maxPoolSize=50
jdbc.minPoolSize=10
jdbc.maxStatements=100
jdbc.testConnection=true

# \u901A\u7528Mapper\u914D\u7F6E
mapper.plugin = tk.mybatis.mapper.generator.MapperPlugin
mapper.Mapper = com.seewo.kishframework.mybatis.base.BaseRepository