package com.cisp.event.api.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Schema(description = "事件模拟对象")
public class EventMockVo {

    @Schema(description = "模拟业务事件的类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "类型不能为空")
    private String type;

    @Schema(description = "事件数据", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "事件数据不能为空")
    private Object data;

    private List<Long> placeIds;

    /**
     * 与当天比相差几天（+）
     */
    private int day;

}
