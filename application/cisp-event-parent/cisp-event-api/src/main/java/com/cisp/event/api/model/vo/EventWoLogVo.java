package com.cisp.event.api.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Schema(description = "事件日志相关日志实体")
@Accessors(chain = true)
public class EventWoLogVo {

    private Long id;

    private Long eventId;

    @Schema(description = "处理方式（上报、通知、转派、处理）")
    private String processMode;

    @Schema(description = "时间")
    private Long time;

    @Schema(description = "处理人")
    private String processUser;

    @Schema(description = "被转派/通知的人")
    private String assignUser;

    @Schema(description = "手机号")
    private String phone;
}
