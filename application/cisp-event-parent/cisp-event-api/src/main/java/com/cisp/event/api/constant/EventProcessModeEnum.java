package com.cisp.event.api.constant;

public enum EventProcessModeEnum {
    /**
     * 直接处理事件
     */
    DEAL((byte)0,"deal"),

    /**
     * 转派事件
     */
    ASSIGN((byte)1,"assign"),

    /**
     * 微信通知
     */
    WX_NOTIFY((byte)2,"WXNotify"),

    /**
     * 平台通知
     */
    IM_NOTIFY((byte)3,"IMNotify"),

    /**
     * 电话语音通知
     */
    PHONE_NOTIFY((byte)4,"voiceNotify"),

    /**
     * 短信通知
     */
    MESSAGE_NOTIFY((byte)5,"messageNotify"),

    /**
     * 上报事件
     */
    REPORT((byte)6,"report"),

    /**
     * 将事件设置为无需处理
     */
    IGNORE((byte)7,"ignore");

    private Byte value;

    private String code;

    EventProcessModeEnum(Byte value,String code){
        this.value = value;
        this.code = code;
    }

    public static EventProcessModeEnum getByCode(String code){
        EventProcessModeEnum[] values = EventProcessModeEnum.values();
        for(EventProcessModeEnum eventProcessModeEnum:values){
            if (eventProcessModeEnum.getCode().equals(code)){
                return eventProcessModeEnum;
            }
        }
        return null;
    }

    public static EventProcessModeEnum getByValue(Byte value){

        EventProcessModeEnum[] values = EventProcessModeEnum.values();
        for(EventProcessModeEnum eventProcessModeEnum:values){
            if (eventProcessModeEnum.getValue().equals(value)){
                return eventProcessModeEnum;
            }
        }
        return null;
    }

    public Byte getValue() {
        return value;
    }

    public String getCode() {
        return code;
    }
}
