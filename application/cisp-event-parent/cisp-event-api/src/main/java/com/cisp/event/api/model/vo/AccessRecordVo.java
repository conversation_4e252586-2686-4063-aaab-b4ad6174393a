package com.cisp.event.api.model.vo;

import com.cisp.infrastructure.common.model.dto.EventProofDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "人车出入记录")
public class AccessRecordVo {

    @Schema(description = "事件id")
    private Long id;

    @Schema(description = "事件种类")
    private String category;

    @Schema(description = "事件源")
    private String srcName;

    @Schema(description = "事件源Id")
    private String srcId;


    @Schema(description = "时间")
    private Long createTime;

    @Schema(description = "项目Id")
    private Long projectId;




}
