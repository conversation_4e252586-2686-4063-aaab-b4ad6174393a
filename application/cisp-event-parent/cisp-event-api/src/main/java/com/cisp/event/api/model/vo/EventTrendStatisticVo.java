package com.cisp.event.api.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.Map;

@Data
@Schema(description = "事件趋势统计")
@Accessors(chain = true)
public class EventTrendStatisticVo {

    @Schema(description = "事件总数量")
    private Long totalNum;

    @Schema(description = "每个时间段的数值")
    private Map<Object,Integer> detail = new HashMap<>();

}
