package com.cisp.event.api.model.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "事件按处理人纬度统计")
public class EventProcessorStatisticDto {

    @Schema(description = "已处理的数量")
    private int finished;


    @Schema(description = "待处理的数量")
    private int pending;

    @Schema(description = "地点")
    private String place;

}
