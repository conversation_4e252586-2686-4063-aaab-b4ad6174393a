package com.cisp.event.api.model.vo.ai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "人员信息")
@NoArgsConstructor
@AllArgsConstructor
public class UserInfoVo {

    @Schema(description = "人员id")
    private String userId;
    @Schema(description = "人员姓名")
    private String userName;
    @Schema(description = "人员照片地址")
    private String photoUrl;

    @Schema(description = "手机号")
    private String tel;

    @Schema(description = "认证号（身份证、学号等）")
    private String certificateNo;


}
