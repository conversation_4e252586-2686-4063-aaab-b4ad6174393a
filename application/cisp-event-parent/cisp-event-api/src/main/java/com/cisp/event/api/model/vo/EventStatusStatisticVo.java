package com.cisp.event.api.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Schema(description = "按状态统计事件数量")
@Accessors(chain = true)
public class EventStatusStatisticVo {

    @Schema(description = "待我处理的事件数量")
    private int ownerNum;

    @Schema(description = "待处理数量")
    private int pendingNum;

    @Schema(description = "已完结数量")
    private int finishedNum;
}
