package com.cisp.event.api.constant;


import java.util.Optional;

public enum AiBoxEventEnum implements EventBaseEnum {
    FACE_DETECT("faceDetect",11,"high"),
    FACE_COMPARE_SUCCESS("faceCompareSuccess",12,"high"),
    FACE_COMPARE_FAILURE("faceCompareFailure",13,"high"),

    SMOKE_ALARM("smokeAlarm",7,"middle"),

    /**
     * FLAME_ALARM,SMOKE_DETECT value在盒子的规范中都为8,需要根据报警信息中valueType进行再次判断
     * 当valueType为1时报警为FLAME_ALARM，故整体value为9;valueType为2对应SMOKE_DETECT,故value为10
     */
    FLAME_ALARM("flameAlarm",9,"superHigh"),
    SMOKE_DETECT("smokeDetect",10,"high"),
    LEAVE_DETECT("leaveDetect",17,"middle"),
    REGION_INTRUSION("regionIntrusion",20,"high"),
    CLIMB_DETECT("climbDetect",89,"middle"),
    STRONG_EXERCISE("strongExercise",46,"superHigh"),
    FALL_DETECT("fallDetect",62,"low"),
    STAY_DETECT("stayDetect",57,"low"),
    GATHER_DETECT("gatherDetect",91,"low"),
    QUICKLY_MOVING("quicklyMoving",58,"middle"),

    // 校园防霸凌
    SCHOOL_BULLY("schoolBully", 100, "superHigh"),

    UNKNOWN("unknown",0,"low");
    private int value;

    private String code;

    private String level;

    AiBoxEventEnum(String code, int value, String level){
        this.value = value;
        this.code = code;
        this.level = level;
    }


    public static Optional<AiBoxEventEnum> getFromValue(int value){
        AiBoxEventEnum[] values = AiBoxEventEnum.values();
        for(AiBoxEventEnum aiBoxEventEnum:values){
            if(aiBoxEventEnum.getValue()==value){
                return Optional.of(aiBoxEventEnum);
            }
        }
        return Optional.empty();
    }

    public static Optional<AiBoxEventEnum> getFromCode(String code){
        AiBoxEventEnum[] values = AiBoxEventEnum.values();
        for(AiBoxEventEnum aiBoxEventEnum:values){
            if(aiBoxEventEnum.getCode().equals(code)){
                return Optional.of(aiBoxEventEnum);
            }
        }
        return Optional.empty();
    }



    @Override
    public int getValue() {
        return value;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getLevel() {
        return level;
    }
}
