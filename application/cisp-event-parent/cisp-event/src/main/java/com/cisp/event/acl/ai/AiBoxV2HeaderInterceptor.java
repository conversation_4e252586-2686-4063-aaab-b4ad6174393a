package com.cisp.event.acl.ai;

import com.github.lianjiatech.retrofit.spring.boot.interceptor.BasePathMatchInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * AIBOX V2版本的请求拦截器
 */
@Slf4j
@Component
public class AiBoxV2HeaderInterceptor extends BasePathMatchInterceptor {

    private static final String AUTH_HEADER = "Authorization";

    @Resource
    private AiBoxV2TokenService tokenService;

    @Override
    protected Response doIntercept(Chain chain) throws IOException {
        String method = chain.request().method();
        String uri = chain.request().url().uri().getPath();
        if (!tokenService.isLogin()){
            tokenService.login();
        }
        String authToken = tokenService.getToken(method, uri);
        Request newReq = chain.request().newBuilder().addHeader(AUTH_HEADER, authToken).build();
        return chain.proceed(newReq);
    }
}
