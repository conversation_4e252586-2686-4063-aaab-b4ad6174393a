package com.cisp.event.application.model.ai.v2;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class PersonInfo extends PersonCreateReq{

    @JsonProperty(value = "person_id")
    private Integer personId;

    @JsonProperty(value = "modeling_type")
    private Integer modelingType;

    @JsonProperty(value = "image_path")
    private String imagePath;

    @JsonProperty(value = "create_time")
    private Integer createTime;

}
