package com.cisp.event.application.service.processor;

import com.cisp.infrastructure.common.util.Jsons;
import com.cisp.infrastructure.config.SystemConfiguration;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class RedisEventQueue implements EventQueue {
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private SystemConfiguration systemConfiguration;

    private static final String EVENT_BUFFER_KEY = "cisp:event:buffer";

    private static final String EVENT_DELAY_BUFFER_KEY = "cisp:event:delay:buffer";

    @Override
    public void addToBuffer(EventElement eventElement) {
        String key =  systemConfiguration.isEdgeDeploy()? EVENT_BUFFER_KEY : EVENT_BUFFER_KEY + ":cloud:";
        redisTemplate.opsForList().leftPush(key, eventElement);
    }

    @Override
    public void addToDelayBuffer(EventElement eventElement) {
        redisTemplate.opsForList().leftPush(EVENT_DELAY_BUFFER_KEY, eventElement);
    }

    @Override
    public List<EventElement> fetchBatch(int batchSize) {
        String key =  systemConfiguration.isEdgeDeploy()? EVENT_BUFFER_KEY : EVENT_BUFFER_KEY + ":cloud:";
        DefaultRedisScript<List> redisScript = new DefaultRedisScript<>();
        redisScript.setScriptText(
                "local temp = " + batchSize +"\n"+
                        "local range = redis.call('LRANGE', KEYS[1], 0, temp-1)\n" +
                        "redis.call('LTRIM', KEYS[1], temp, -1)\n" +
                        "return range");
        redisScript.setResultType(List.class);
        List<String> keys = Collections.singletonList(key);
        List range = (List) redisTemplate.execute(redisScript, keys);
        List<EventElement> eventElements = (List<EventElement>) range.stream().map(tmp -> Jsons.objToObj(tmp, EventElement.class)).collect(Collectors.toList());
        return eventElements;
    }

    @Override
    public List<EventElement> fetchAll(String key) {
        DefaultRedisScript<List> redisScript = new DefaultRedisScript<>();
        redisScript.setScriptText(
                "local temp = redis.call('LLEN', KEYS[1])\n" +
                        "local events = redis.call('LRANGE', KEYS[1], 0, temp-1)\n" +
                        "redis.call('LTRIM', KEYS[1], temp, -1)\n" +
                        "return events");
        redisScript.setResultType(List.class);
        List<String> keys = Collections.singletonList(key);
        List range = (List) redisTemplate.execute(redisScript, keys);
        if (CollectionUtils.isEmpty(range)) {
            return Collections.emptyList();
        }
        List<EventElement> eventElements = (List<EventElement>) range.stream().map(tmp -> Jsons.objToObj(tmp, EventElement.class)).collect(Collectors.toList());
        return eventElements;
    }

    @Override
    public int getBufferSize() {
        String key =  systemConfiguration.isEdgeDeploy()? EVENT_BUFFER_KEY : EVENT_BUFFER_KEY + ":cloud:";
        Long size = redisTemplate.opsForList().size(key);
        return size != null ? size.intValue() : 0;
    }
}
