package com.cisp.event.infrastructure;

import lombok.Data;

@Data
public class TokenResponseDto {
    String AccessCode;
    String Encryption;
    String AccessToken;

    public TokenResponseDto(String accessCode, String encryption, String accessToken) {
        AccessCode = accessCode;
        Encryption = encryption;
        AccessToken = accessToken;
    }

    public TokenResponseDto() {
    }
}
