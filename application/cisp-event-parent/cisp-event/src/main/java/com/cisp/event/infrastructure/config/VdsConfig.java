package com.cisp.event.infrastructure.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class VdsConfig {


    @Value("${vds.name:loadmin}")
    private String name ;

    @Value("${vds.pwd:Admin_123456}")
    private String pwd;

    @Value("${vds.url:http://172.20.127.250:8088}")
    private String url;

}
