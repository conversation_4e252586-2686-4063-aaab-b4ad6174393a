package com.cisp.event.infrastructure.config;

import com.cisp.infrastructure.config.TaskConfiguration;
import com.cisp.infrastructure.service.ScheduledExecutorServiceFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ConcurrentTaskScheduler;

@Configuration
public class EventTaskConfiguration {

    @Autowired
    private TaskConfiguration taskConfiguration;


    @Bean(value = "cispScheduleExecutorFactory")
    public ScheduledExecutorServiceFactory eventScheduleExecutorFactory(){
        return taskConfiguration.createScheduledExecutor("cisp-schedule-event",Runtime.getRuntime().availableProcessors());
    }


    @Bean(value = "cispScheduleExecutor")
    public ConcurrentTaskScheduler eventScheduleExecutor(){
        return  taskConfiguration.createScheduler(eventScheduleExecutorFactory());
    }






}
