package com.cisp.event.domain.repository;

import com.cisp.event.domain.entity.SecurityReportEntity;
import com.seewo.kishframework.mybatis.base.BaseRepository;
import com.seewo.kishframework.page.Page;
import com.seewo.kishframework.page.Pageable;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface SecurityReportRepository extends BaseRepository<SecurityReportEntity>, Mapper<SecurityReportEntity> {

    Page<SecurityReportEntity> queryPage(@Param("type") String type,
                                    @Param("beginTime") Long beginTime,
                                    @Param("endTime") Long endTime,
                                    Pageable pageRequest);

    List<SecurityReportEntity> checkReportExist(@Param("type") String type,
                          @Param("position") Integer position);

    List<SecurityReportEntity> getReports(@Param("type") String type);
}