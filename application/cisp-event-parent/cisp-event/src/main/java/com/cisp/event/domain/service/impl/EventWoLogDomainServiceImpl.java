package com.cisp.event.domain.service.impl;

import com.cisp.event.domain.entity.EventWoLogEntity;
import com.cisp.event.domain.repository.EventWoLogRepository;
import com.cisp.event.domain.service.EventWoLogDomainService;
import com.seewo.kishframework.idworker.IdWorkers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Service
@Slf4j
public class EventWoLogDomainServiceImpl implements EventWoLogDomainService {
    @Autowired
    private EventWoLogRepository eventWoLogRepository;
    @Override
    public Long save(EventWoLogEntity entity) {
        entity.setId(IdWorkers.idWorker().nextId());
        eventWoLogRepository.fillCreated(entity);
        eventWoLogRepository.insertSelective(entity);
        return entity.getId();
    }

    @Override
    public List<EventWoLogEntity> findByEventId(Long eventId) {
        Example example = new Example(EventWoLogEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("eventId",eventId);
        return eventWoLogRepository.selectByExample(example);
    }
}
