package com.cisp.event.domain.entity;

import com.seewo.kishframework.mybatis.base.BaseEntity;
import javax.persistence.*;
import java.util.Objects;

@Table(name = "event")
public class EventEntity extends BaseEntity {
    @Id
    private Long id;

    /**
     * 事件类别:视频事件、人脸事件
     */
    private Byte category;

    /**
     * 事件类型
     */
    private Integer type;

    private Byte level;

    @Column(name = "place_id")
    private String placeId;

    @Column(name = "src_id")
    private String srcId;

    @Column(name = "report_type")
    private Byte reportType;

    private Byte status;

    /**
     * 处理人
     */
    @Column(name = "user_ids")
    private String userIds;

    /**
     * 相关人
     */
    @Column(name = "related_user")
    private String relatedUser;

    private String remark;

    private String ext;

    @Column(name = "tenant_id")
    private String tenantId;


    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public boolean check(){
        return Objects.nonNull(category) && Objects.nonNull(type);
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * @return id
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return level
     */
    public Byte getLevel() {
        return level;
    }

    /**
     * @param level
     */
    public void setLevel(Byte level) {
        this.level = level;
    }

    /**
     * @return place_id
     */
    public String getPlaceId() {
        return placeId;
    }

    /**
     * @param placeId
     */
    public void setPlaceId(String placeId) {
        this.placeId = placeId;
    }

    /**
     * @return src_id
     */
    public String getSrcId() {
        return srcId;
    }

    /**
     * @param srcId
     */
    public void setSrcId(String srcId) {
        this.srcId = srcId;
    }

    /**
     * @return report_type
     */
    public Byte getReportType() {
        return reportType;
    }

    /**
     * @param reportType
     */
    public void setReportType(Byte reportType) {
        this.reportType = reportType;
    }

    /**
     * @return statue
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * @param status
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * @return user_ids
     */
    public String getUserIds() {
        return userIds;
    }

    /**
     * @param userIds
     */
    public void setUserIds(String userIds) {
        this.userIds = userIds;
    }

    /**
     * @return ext
     */
    public String getExt() {
        return ext;
    }

    /**
     * @param ext
     */
    public void setExt(String ext) {
        this.ext = ext;
    }

    public String getRelatedUser() {
        return relatedUser;
    }

    public void setRelatedUser(String relatedUser) {
        this.relatedUser = relatedUser;
    }

    public Byte getCategory() {
        return category;
    }

    public void setCategory(Byte category) {
        this.category = category;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
}