package com.cisp.event.application.model.query;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.cisp.event.api.constant.EventEnum;
import com.cisp.event.api.model.query.EventQuery;
import com.cisp.infrastructure.common.query.AbsQueryBuilder;
import com.seewo.kishframework.page.PageRequest;
import com.seewo.kishframework.page.PageRequestUtils;

import java.util.*;
import java.util.stream.Collectors;

public class EventQueryBuilder extends AbsQueryBuilder {

    private EventQuery eventQuery;

    private String tenantId;


    private Boolean orderByCreatedAt = true;

    private List<String> processUsersParam;

    @Override
    public String getOrderBy() {
//        StringBuilder orderStr = new StringBuilder();
//        orderStr.append("level,");
//        orderStr.append("status desc,");
//        if (this.orderByCreatedAt) {
//            orderStr.append("created_at desc");
//        }
//        return orderStr.toString();
        return null;
    }

    @Override
    public PageRequest getPageRequest() {
        Integer page = (this.getCurrentPage() != null && this.getCurrentPage() > 0) ? this.getCurrentPage() - 1 : 0;
        Integer size = (this.getPageSize() != null) ? this.getPageSize() : 20;
        return PageRequestUtils.from(page, size, this.getOrderBy());
    }


//    public Long getProjectId() {
//        return this.getEventQuery() == null ? null : this.getEventQuery().getProjectId();
//    }

    public Long getBeginTime() {
        return this.getEventQuery() == null  ? null : this.getEventQuery().getBeginTime();
    }

    public Long getEndTime() {
        return this.getEventQuery() == null  ? null : this.getEventQuery().getEndTime();
    }

    public String getEventType() {
        return this.getEventQuery() == null ? null : this.getEventQuery().getEventType();
    }

    public List<String> getEventTypes() {
        return (this.getEventQuery() == null || StrUtil.isBlankOrUndefined(this.getEventQuery().getEventTypes())) ?
                Collections.emptyList() : Arrays.asList(this.getEventQuery().getEventTypes().split(","));
    }

    public Byte getEventLevel() {
        return this.getEventQuery() == null ? null : EventEnum.stringToByte("level", this.getEventQuery().getEventLevel());
    }

    public Set<Byte> getEventLevels() {
        return (this.getEventQuery() == null || StrUtil.isBlankOrUndefined(this.getEventQuery().getEventLevels())) ? Collections.emptySet() :
                Arrays.stream(this.getEventQuery().getEventLevels().split(","))
                        .map(level -> EventEnum.stringToByte("level", level))
                        .collect(Collectors.toSet());
    }

    public Byte getEventStatus() {
        return this.getEventQuery() == null ? null : EventEnum.stringToByte("status", this.getEventQuery().getEventStatus());
    }

    public Set<Byte> getEventStatuses() {
        return (this.getEventQuery() == null || StrUtil.isBlankOrUndefined(this.getEventQuery().getEventStatuses())) ? Collections.emptySet() :
                Arrays.stream(this.getEventQuery().getEventStatuses().split(","))
                        .map(status -> EventEnum.stringToByte("status", status))
                        .collect(Collectors.toSet());
    }

    public Byte getReportType() {
        return this.getEventQuery() == null ? null : EventEnum.stringToByte("reportType", this.getEventQuery().getReportType());
    }

//    public Boolean getTwinFilter() {
//        return this.getEventQuery() == null ? null : this.getEventQuery().getTwinFilter();
//    }

    public String getPlaceId() {
        return this.getEventQuery() == null ? null : this.getEventQuery().getPlaceId();
    }

    public Long getId() {
        return this.getEventQuery() == null ? null : this.getEventQuery().getId();
    }


    public String getSrcId() {
        return this.getEventQuery() == null ? null : this.getEventQuery().getSrcId();
    }


    public EventQuery getEventQuery() {
        return eventQuery;
    }

    public EventQueryBuilder setEventQuery(EventQuery eventQuery) {
        this.eventQuery = eventQuery;
        return this;
    }

    public boolean getOrderByCreatedAt() {
        return orderByCreatedAt;
    }

    public EventQueryBuilder setOrderByCreatedAt(Boolean orderByCreatedAt) {
        this.orderByCreatedAt = orderByCreatedAt;
        return this;
    }

    public String getProcessUser() {
        return this.getEventQuery() == null  ? null : this.getEventQuery().getProcessUser();
    }

    public String getDirector(){
        return this.getEventQuery() == null  ? null : this.getEventQuery().getDirector();
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
//
//    public void setProcessUsersParam(List<String> processUsersParam) {
//        this.processUsersParam = processUsersParam;
//    }
}
