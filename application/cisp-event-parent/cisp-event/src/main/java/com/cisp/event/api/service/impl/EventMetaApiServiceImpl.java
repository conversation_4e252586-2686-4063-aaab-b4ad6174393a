package com.cisp.event.api.service.impl;

import com.cisp.event.api.model.meta.EventBaseMetaDto;
import com.cisp.event.api.model.meta.EventTypeMetaDto;
import com.cisp.event.api.service.EventMetaApiService;
import com.cisp.event.domain.entity.EventMetaEntity;
import com.cisp.event.domain.service.EventMetaDomainService;
import com.cisp.event.infrastructure.convert.EventMetaMapper;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;


@DubboService(protocol = "dubbo")
public class EventMetaApiServiceImpl implements EventMetaApiService {
    @Autowired
    private EventMetaDomainService eventMetaDomainService;
    @Autowired
    private EventMetaMapper eventMetaMapper;

    @Override
    public EventTypeMetaDto getEventTypeByBizTypeAndExtLabel(String bizType, String key, String code) {
        EventMetaEntity eventMetaEntity = eventMetaDomainService.findByBizTypeAndExtLabel(bizType, key, code);
        return eventMetaMapper.toTypeDto(eventMetaEntity);
    }
}
