package com.cisp.event.domain.repository;

import com.cisp.event.domain.entity.TraceRecordEntity;
import com.cisp.infrastructure.common.mapper.InsertListExtMapper;
import com.seewo.kishframework.mybatis.base.BaseRepository;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface TraceRecordRepository extends BaseRepository<TraceRecordEntity>, Mapper<TraceRecordEntity>, InsertListExtMapper<TraceRecordEntity> {


    List<TraceRecordEntity> getList(@Param("beginTime") Long beginTime,@Param("endTime") Long endTime);


    List<TraceRecordEntity> getUsersLastTraceRecords(@Param("userIds") List<String> userIds);
}