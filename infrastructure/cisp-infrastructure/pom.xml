<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cisp-parent</artifactId>
        <groupId>com.cisp</groupId>
        <version>${revision}</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.cisp</groupId>
    <artifactId>cisp-infrastructure</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>

    <properties>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.cisp</groupId>
                <artifactId>cisp-bom</artifactId>
                <version>${revision}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.cisp</groupId>
            <artifactId>cisp-dependencies-service</artifactId>
            <type>pom</type>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.seewo</groupId>
            <artifactId>seewo-usercenter-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.seewo.cstore</groupId>
            <artifactId>cstore-v3-sdk-internal</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.lianjiatech</groupId>
            <artifactId>retrofit-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel-core</artifactId>
            <version>3.1.4</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.seewo.mis.common</groupId>
            <artifactId>seewo-mis-common</artifactId>
            <version>3.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-exec</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.cisp</groupId>
            <artifactId>cisp-common-model</artifactId>
        </dependency>
        <dependency>
            <groupId>org.java-websocket</groupId>
            <artifactId>Java-WebSocket</artifactId>
        </dependency>
    </dependencies>

</project>