package com.cisp.infrastructure.cstore;

import com.seewo.cstore.StorageApiService;
import com.seewo.cstore.common.auth.Configuration;
import com.seewo.cstore.model.req.UploadSimpleFileRequest;
import com.seewo.cstore.model.res.UploadResult;
import com.seewo.kishframework.exception.PlatformExceptionCode;

import java.io.File;

/**
 * 封闭cstore客户端，用来适配自定义的filekey进行文件上传
 */
public class CstoreClient extends com.seewo.cstore.CstoreClient {

    private final String keyPrefix;
    private final StorageApiService apiService;

    private final String host;

    public CstoreClient(String keyPrefix, Configuration cfg) {
        super(cfg);
        this.keyPrefix = keyPrefix;
        this.apiService = new StorageApiService(cfg);
        this.host = cfg.getCstoreHost();
    }

    /**
     * 自定义filekey 上传文件
     * @param fileKey
     * @param file
     * @return
     */
    public UploadResult uploadSimpleFile(String fileKey, File file){
        UploadSimpleFileRequest request = new UploadSimpleFileRequest.Builder(file)
                                            .needQuickUpload(true).build();
        return SimpleUploader.getInstance().uploadSimpleFile(fileKey, apiService, request);
    }

    /**
     * + 号替换成下划线，/ 替换成-划线
     * @param params
     * @param needReplace 是否需要对特定字符进行替换
     * @return
     */
    public String customerFileKey(Boolean needReplace, String... params){
        if (params.length == 0){
            throw PlatformExceptionCode.COMMON_PARAM_EXCEPTION.exception();
        }
        StringBuilder sb = new StringBuilder(keyPrefix).append("/");
        if (needReplace){
            String connectStr = String.join("-", params);
            sb.append(connectStr.replaceAll("\\+", "_"));
        }else {
            String connectStr = String.join("", params);
            sb.append(connectStr);
        }
        return sb.toString();
    }

    public String getHost(){
        return this.host;
    }
}
