package com.cisp.infrastructure.common.jaxrs;

import com.alibaba.dubbo.common.Constants;
import com.alibaba.dubbo.common.extension.Activate;
import com.cisp.infrastructure.common.util.Jsons;
import com.cisp.infrastructure.common.util.PathMatcherUtils;
import com.cisp.infrastructure.config.LogConfiguration;
import com.seewo.kishframework.spring.context.SpringContextUtils;
import com.seewo.kishframework.spring.log.dubbo.AbsLoggingFilter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.rpc.Invocation;
import org.apache.dubbo.rpc.Invoker;
import org.apache.dubbo.rpc.Result;
import org.apache.dubbo.rpc.RpcException;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


@Slf4j
@Activate(group = Constants.PROVIDER, order = Integer.MAX_VALUE - 10)
public class DwsRestProviderLogFilter extends AbsLoggingFilter {

    private static final String PROTOCOL_REST = "rest";

    private static final String LOG_CONF_KEY = "dws:runtime:log:conf";


    public DwsRestProviderLogFilter() {
        super("in");
    }

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        String protocol = invoker.getUrl().getProtocol();
        if (!PROTOCOL_REST.equals(protocol)) {
            return invoker.invoke(invocation);
        }
        List<String> excludes = (List<String>) getLogConf().getLeft();
        List<String> includes = (List<String>) getLogConf().getRight();
        String methodName = invocation.getMethodName();
        String fullName = invoker.getInterface().getName() + "." + methodName;

        //过滤日志
        if (CollectionUtils.isNotEmpty(excludes)) {
            if (excludes.stream().anyMatch(s -> PathMatcherUtils.isPathMatch(fullName, s))) {
                return invoker.invoke(invocation);
            }
        }
        if (CollectionUtils.isNotEmpty(includes)) {
            if (includes.stream().anyMatch(include -> PathMatcherUtils.isPathMatch(fullName, include))) {
                return super.invoke(invoker, invocation);
            } else {
                return invoker.invoke(invocation);
            }
        }
        return super.invoke(invoker, invocation);
    }

    @Override
    protected boolean isCallChainBody() {
        return true;
    }

    @Override
    protected boolean isCallChainHeader() {
        return true;
    }

    @Override
    protected int getCallChainBodyLimit() {
        return 200;
    }


    private Pair getLogConf() {
        LogConfiguration bean = SpringContextUtils.applicationContext().getBean(LogConfiguration.class);
        RedisTemplate redisTemplate = (RedisTemplate) SpringContextUtils.applicationContext().getBean("redisTemplate");
        List<String> exclude = Jsons.strToArray(bean.getExclude(), String.class);
        List<String> include = Jsons.strToArray(bean.getInclude(), String.class);
        if (redisTemplate.hasKey(LOG_CONF_KEY)) {
            Object o = redisTemplate.opsForHash().get(LOG_CONF_KEY,"exclude");
            Object o2 = redisTemplate.opsForHash().get(LOG_CONF_KEY,"include");
            if(Objects.nonNull(o)){
                exclude.addAll(Jsons.objToObj(o, ArrayList.class));
            }
            if(Objects.nonNull(o2)){
                include.addAll(Jsons.objToObj(o2, ArrayList.class));
            }
        }
        return Pair.of(exclude, include);

    }
}
