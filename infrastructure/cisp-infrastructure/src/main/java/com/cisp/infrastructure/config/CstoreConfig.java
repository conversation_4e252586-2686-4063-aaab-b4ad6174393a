package com.cisp.infrastructure.config;

import cn.hutool.extra.spring.SpringUtil;
import com.cisp.infrastructure.cstore.CstoreClient;
import com.cisp.infrastructure.cstore.CstoreClientFactory;
import com.seewo.cstore.constant.EnvironmentEnum;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Data
@Configuration
@ConfigurationProperties(prefix = "cstore")
public class CstoreConfig implements InitializingBean {

    private Map<String, ConfigData> config;

    @Override
    public void afterPropertiesSet() {
        if (Objects.nonNull(config)){
            for (String key : config.keySet()) {
                ConfigData configData = config.get(key);
                CstoreClient client = this.createClient(key, configData.getKeyPrefix());
                SpringUtil.registerBean(configData.getName(), client);
            }
        }
    }

    private CstoreClient createClient(String configId, String keyPrefix) {
        ConfigData configData = config.get(configId);
        if (Objects.nonNull(configData)) {
            com.seewo.cstore.common.auth.Configuration cc = new com.seewo.cstore.common.auth.Configuration(configData.appId, from(configData.env), configData.host);
            return CstoreClientFactory.getCstoreClient(keyPrefix, cc);
        }
        return null;
    }

    private EnvironmentEnum from(String envStr) {
        Optional<EnvironmentEnum> envEnum = Arrays.stream(EnvironmentEnum.values()).filter(e -> e.name().equalsIgnoreCase(envStr)).findFirst();
        return envEnum.orElse(EnvironmentEnum.PRO);
    }

    @Data
    @NoArgsConstructor
    @ToString
    public static class ConfigData {

        /**
         * beanName
         */
        private String name;

        /**
         * 应用标识ID
         */
        private String appId;

        /**
         * 环境标枳, DEV/FAT/PRO
         */
        private String env;

        private String host = "";

        /**
         * fileKey的前缀，例如 dws-resource
         */
        private String keyPrefix = "";
    }

}
