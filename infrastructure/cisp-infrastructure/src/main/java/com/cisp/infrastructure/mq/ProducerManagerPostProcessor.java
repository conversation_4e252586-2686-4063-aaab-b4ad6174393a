package com.cisp.infrastructure.mq;

import com.seewo.framework.mq.client.ProducerManager;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

@Component
public class ProducerManagerPostProcessor implements BeanPostProcessor {
    @Value("${mq.msg.timeout:6000}")
    private String msgTimeOut;

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        if(bean instanceof ProducerManager){
            ProducerManager producerManager = (ProducerManager) bean;
            producerManager.getProducer().setSendMsgTimeout(Integer.valueOf(msgTimeOut));
        }
        return BeanPostProcessor.super.postProcessAfterInitialization(bean, beanName);
    }
}
