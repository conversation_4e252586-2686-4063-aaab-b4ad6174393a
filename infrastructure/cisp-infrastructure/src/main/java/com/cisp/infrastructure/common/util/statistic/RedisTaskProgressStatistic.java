package com.cisp.infrastructure.common.util.statistic;

import cn.hutool.extra.spring.SpringUtil;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>分布式任务进度统计-Redis实现<p/>
 *
 * <AUTHOR>
 */
public class RedisTaskProgressStatistic implements TaskProgressStatistic {
    /**
     * <p>管理者RedisKey</>
     */
    private String redisKey;
    /**
     * <p>任务名称</>
     */
    private String name;
    /**
     * <p>任务完成数</>
     */
    private long finished;
    /**
     * <p>任务总数</>
     */
    private long total = 1;
    /**
     * <p>总进度占比</>
     */
    private int ratio;

    private RedisTemplate<String, Object> redisTemplate;

    public RedisTaskProgressStatistic() {
    }

    public RedisTaskProgressStatistic(String name, int ratio) {
        this.name = name;
        this.ratio = ratio;

        this.redisTemplate = SpringUtil.getBean(RedisTemplate.class);
    }

    public RedisTaskProgressStatistic(Object name, Object value) {
        this.name = name.toString();
        String[] arr = value.toString().split(",");

        Map<String, String> data = new HashMap<>();
        for (String tmp : arr) {
            String[] values = tmp.split(":");
            data.put(values[0], values[1]);
        }
        this.finished = Long.parseLong(data.get("finished"));
        this.total = Long.parseLong(data.get("total"));
        this.ratio = Integer.parseInt(data.get("ratio"));

        this.redisTemplate = SpringUtil.getBean(RedisTemplate.class);
    }

    public void setRedisKey(String redisKey) {
        this.redisKey = redisKey;
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public void finishedIncrease(long finished) {
        this.finished += finished;

        redisTemplate.opsForHash().put(this.redisKey, this.name, this.toString());
    }

    @Override
    public void setFinished(long finished) {
        this.finished = Math.max(finished, this.finished);

        redisTemplate.opsForHash().put(this.redisKey, this.name, this.toString());
    }

    @Override
    public void setTotal(long total) {
        this.total = Math.max(total, 0);

        redisTemplate.opsForHash().put(this.redisKey, this.name, this.toString());
    }

    @Override
    public int calcProgress() {
        int progress = (int) (100 * this.finished / this.total);
        return Math.min(progress, 100);
    }

    @Override
    public int getRatio() {
        return this.ratio;
    }

    @Override
    public void setRatio(int ratio) {
        this.ratio = ratio;
        redisTemplate.opsForHash().put(this.redisKey, this.name, this.toString());
    }

    @Override
    public void taskCompeted() {
        this.finished = this.total;
        redisTemplate.opsForHash().put(this.redisKey, this.name, this.toString());
    }

    @Override
    public String toString() {
        return "finished:" + finished + ",total:" + total + ",ratio:" + ratio;
    }
}
