package com.cisp.infrastructure.common.exception;

import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.Filter;
import org.apache.dubbo.rpc.Invocation;
import org.apache.dubbo.rpc.Invoker;
import org.apache.dubbo.rpc.Result;

@Activate(
        group = {"provider", "consumer"},
        after = {"exception"},
        order = Integer.MAX_VALUE
)
@Slf4j
public class CustomerExceptionFilter implements Filter {
    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RuntimeException {
        Result result = invoker.invoke(invocation);
        if (result.hasException()) {
            Throwable exception = result.getException();
            result.setException(null);
            // todo 如果 异常非 自定义异常，单独处理返回具体的异常报错内容，方便定位问题！
            //                          而不是返回  "系统繁忙，请稍后再试",  一脸懵。
            /*if (exception instanceof BusinessException
                    || exception instanceof SystemException
                    || exception instanceof CodeBaseException) {

            } else {
                throw (RuntimeException) exception;
            }*/
            //交给[com.cisp.infrastructure.common.exception.CustomExceptionMapper]处理
            throw (RuntimeException) exception;
        }

        return result;
    }
}
