package com.cisp.infrastructure.common.util;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.nio.charset.StandardCharsets;

@Slf4j
public class FileDownloader {
    public static void main(String[] args) {
        String fileUrl = "https://cos-private.seewo.com/en-digital-pri/encloud-171430552475547164c1e?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKIDJUXMxJRLzPaeMp20jDSTFl23pLcdPwDF%26q-sign-time%3D1714443842%3B1714530242%26q-key-time%3D1714443842%3B1714530242%26q-header-list%3Dhost%26q-url-param-list%3D%26q-signature%3D50a7130a0ae8435d4b6fe0bdb19f8e0166d0604b";


        String fileContent = downloadFile(fileUrl);

        System.out.println("File content:\n" + fileContent.replace("\uFEFF", ""));
    }

    public static String downloadFile(String fileUrl) {
        InputStream inputStream = null;
        BufferedReader reader = null;
        StringBuffer content = null;
        try {
            URL url = new URL(fileUrl);
            inputStream = url.openStream();
            reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));

            content = new StringBuffer(); // 使用StringBuffer代替StringBuilder

            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n"); // 保留换行符
            }
        } catch (IOException e) {
            log.error(e.getMessage());
        } finally {
            try {
                if (reader != null) {
                    reader.close();
                }
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                log.error(e.getMessage());
            }
        }

        return content.toString();
    }

}
