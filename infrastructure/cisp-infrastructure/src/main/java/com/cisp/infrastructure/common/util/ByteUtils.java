package com.cisp.infrastructure.common.util;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 字节工具类
 */
public class ByteUtils {

    public static Byte boolToByte(Boolean value) {
        Byte res = 0x00;
        if (value == null || !value){
            return res;
        }
        if (value){
            res = 0x01;
        }
        return res;
    }

    public static Boolean byteToBoolean(Byte value) {
        Boolean res = false;
        if (value == null || value == 0x00){
            return res;
        }
        if (value == 0x01){
            res = true;
        }
        return res;
    }

}
