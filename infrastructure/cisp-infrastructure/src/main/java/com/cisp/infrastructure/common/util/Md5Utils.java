package com.cisp.infrastructure.common.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.DigestUtils;

public class Md5Utils extends DigestUtils {

    public static String getMd5Str(String originStr) {
        if (StringUtils.isBlank(originStr)) {
            return Strings.EMPTY;
        }
        return md5DigestAsHex(originStr.getBytes());
    }
}
