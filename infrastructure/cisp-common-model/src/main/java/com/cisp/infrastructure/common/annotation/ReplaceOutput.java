package com.cisp.infrastructure.common.annotation;

import java.lang.annotation.*;

/**
 * 出参替换注解，将出参中资源的key替换为对应的url
 * 要求要替换的目标参数一定要为实体，并且需要有projectId字段（用来查询资源池信息）
 * 要求出参为集合或者对象
 */
@Documented
@Inherited
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ReplaceOutput {
    //要替换的参数对应的下标
    int parameterIndex();
    //要替换的对象字段
    String fieldName();
    //要替换的字段里面的key
    String keyName();
}
