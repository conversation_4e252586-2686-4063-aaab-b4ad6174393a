package com.cisp.infrastructure.common.annotation;


import java.lang.annotation.*;

@Documented
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ListenArg {


    /**
     * 监听的参数
     * @return
     */
    String arg() default "";


    /**
     * 参数中的属性
     * @return
     */
    String attribute() default "";


    /**
     * 执行的动作
     * @return
     */
    String action() default "";

}
