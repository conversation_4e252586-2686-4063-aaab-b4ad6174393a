package com.cisp.infrastructure.common.util;

import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

public class StringBusUtils {

    public static String buildMultiString(String... strings) {
        if (Objects.nonNull(strings) && strings.length > 0) {
            StringBuilder stringBuilder = new StringBuilder();
            for (String str : strings) {
                if (StringUtils.isNotBlank(str)) {
                    stringBuilder.append(str);
                }
            }
            return stringBuilder.toString();
        }
        return "";
    }


}
