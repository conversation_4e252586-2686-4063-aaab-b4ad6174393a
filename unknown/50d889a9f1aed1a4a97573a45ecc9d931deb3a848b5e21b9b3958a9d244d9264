<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.cisp</groupId>
        <artifactId>cisp-access-parent</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>cisp-access</artifactId>
    <packaging>jar</packaging>
    <version>${revision}</version>


    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
        <!--  MyBatis Generator  -->
        <mybatis.generator.basedir>${basedir}</mybatis.generator.basedir>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.cisp</groupId>
                <artifactId>cisp-bom</artifactId>
                <version>${revision}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.cisp</groupId>
            <artifactId>cisp-dependencies-service</artifactId>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>com.cisp</groupId>
            <artifactId>cisp-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cisp</groupId>
            <artifactId>cisp-access-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.seewo.ucp.operation</groupId>
            <artifactId>ucp-operation-rpc-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.seewo.iot</groupId>
            <artifactId>seewo-iot-sdk-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.cisp</groupId>
            <artifactId>api-gateway-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cisp</groupId>
            <artifactId>api-gateway-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cisp</groupId>
            <artifactId>pts-agent</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cisp</groupId>
            <artifactId>cisp-identity-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cisp</groupId>
            <artifactId>cisp-device-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cisp</groupId>
            <artifactId>cisp-system-api</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source> <!-- depending on your project -->
                    <target>1.8</target> <!-- depending on your project -->
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>attach-sources-mixin</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                        <configuration>
                            <classifier>mixin-sources</classifier>
                            <excludes>
                                <!--注意从编译结果目录开始算目录结构-->
                                <exclude>**/App.*</exclude>
                                <exclude>META-INF/app.properties</exclude>
                                <exclude>application.yaml</exclude>
                                <exclude>application-local.properties</exclude>
                                <exclude>log4j2.xml</exclude>
                                <exclude>spy.properties</exclude>
                            </excludes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>package-mixin</id>
                        <phase>package</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                        <configuration>
                            <classifier>mixin</classifier>
                            <excludes>
                                <!--注意从编译结果目录开始算目录结构-->
                                <exclude>**/AccessApp.*</exclude>
                                <exclude>META-INF/app.properties</exclude>
                                <exclude>application.yaml</exclude>
                                <exclude>application-local.properties</exclude>
                                <exclude>log4j2.xml</exclude>
                                <exclude>spy.properties</exclude>
                            </excludes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    
</project>