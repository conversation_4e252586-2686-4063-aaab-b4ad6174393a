package com.cisp.video.api.model.vo;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 流信息
 */
@Data
@Accessors(chain = true)
public class StreamInfoVo {

    private String url;

    private String hlsUrl;
    private String flvUrl;
    private String rtmpUrl;
    private String webrtcUrl;
    private String websocketFlvUrl;
    private String sessionId;

}
