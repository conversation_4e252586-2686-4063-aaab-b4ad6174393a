package com.cisp.system.api.service;

import com.seewo.kishframework.rest.VoidRestResponse;
import com.cisp.system.api.constant.Constants;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.tags.Tag;

import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

@Hidden
@Tag(name = "HealthApiService", description = "健康检查")
@Path(Constants.API_PREFIX + "/health")
@Consumes({MediaType.APPLICATION_JSON})
@Produces({MediaType.APPLICATION_JSON})
public interface HealthApiService {

    @GET
    @Path("/check")
    VoidRestResponse check();
}
