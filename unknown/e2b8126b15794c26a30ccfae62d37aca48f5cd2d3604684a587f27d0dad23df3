package com.cisp.system.api.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.ws.rs.QueryParam;

@Schema(description = "字典数据查询")
@Data
public class DictDataQuery {
    @Schema(description = "字典数据类型")
    @QueryParam("type")
    private String dictType;

    @Schema(description = "字典数据键")
    @QueryParam(value = "label")
    private String dictKey;



}
