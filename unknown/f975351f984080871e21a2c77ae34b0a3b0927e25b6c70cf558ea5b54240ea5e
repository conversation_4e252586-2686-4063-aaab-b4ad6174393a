<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cisp.device.domain.repository.DeviceRepository">
  <resultMap id="BaseResultMap" type="com.cisp.device.domain.entity.DeviceEntity">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="device_id" jdbcType="VARCHAR" property="deviceId" />
    <result column="gbs_id" jdbcType="VARCHAR" property="gbsId" />
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
    <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
    <result column="platform_code" jdbcType="VARCHAR" property="platformCode" />
    <result column="channel_number" jdbcType="INTEGER" property="channelNumber" />
    <result column="channel_id" jdbcType="VARCHAR" property="channelId" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="place_id" jdbcType="VARCHAR" property="placeId" />
    <result column="ext" jdbcType="LONGNVARCHAR" property="ext" />
    <result column="created_at" jdbcType="BIGINT" property="createdAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_at" jdbcType="BIGINT" property="updatedAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <resultMap id="DeviceStatusStatVo" type="com.cisp.device.api.model.vo.DeviceStatusStatVo">
    <result property="onlineNum" column="onlineNum"/>
    <result property="offlineNum" column="offlineNum" />
  </resultMap>

  <select id="queryPage" resultMap="BaseResultMap">
    select * from device where id > 0
    AND tenant_id = #{tenantId}
    <if test="type != null">
      AND type = #{type}
    </if>
    <if test="status != null">
      AND `status` = #{status}
    </if>
    <if test="placeId != null and placeId != ''">
      AND IFNULL(JSON_EXTRACT(ext, '$.placeIds'),'') LIKE CONCAT('%', #{placeId}, '%')
    </if>
    <if test="projectId != null">
      AND `project_id` = #{projectId}
    </if>
    <if test="searchKey != null and searchKey != ''">
      AND CONCAT(IFNULL(ip,''),IFNULL(gbs_id,''),IFNULL(sn,''),IFNULL(device_id,''),IFNULL(name,'')) LIKE CONCAT('%', #{searchKey}, '%')
    </if>
  </select>

  <select id="findAll" resultMap="BaseResultMap">
    select id,place_id,status,type,device_id,sn,gbs_id from device where id>0
  </select>

  <update id="erasePlaces" parameterType="java.util.List">
    update device
    set place_id = null
    where project_id = #{projectId}
    and place_id in
    <foreach collection="placeIds" index="index" item="item" separator="," open="(" close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </update>

  <select id="findByCompositeKeys" resultMap="BaseResultMap">
    select * from device
    where (gbs_id, platform_code) in
    <foreach collection="keys" item="item" open="(" separator="," close=")">
      (#{item.gbsId}, #{item.platformCode})
    </foreach>
  </select>

  <select id="countDeviceStatusByTenantAndType" resultMap="DeviceStatusStatVo">
    select
        SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) AS onlineNum,
        SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) AS offlineNum
    from device where
    <if test="tenantId != null">
      tenant_id = #{tenantId}
    </if>
    <if test="type != null">
      AND type = #{type}
    </if>
  </select>

</mapper>