package com.cisp.identity.api.model.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;

@Schema(description = "用户信息")
@Data
@ToString
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserVo {
    /**
     * 用户uid
     */
    @Schema(description = "id")
    private String id;

    /**
     * 昵称
     */
    @Schema(description = "昵称")
    private String nickName;

    /**
     * 真实姓名
     */
    @Schema(description = "真实姓名")
    private String realName;

    /**
     * 账号
     */
    @Schema(description = "账号")
    private String account;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String phone;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String email;

    /**
     * 头像图片url
     */
    @Schema(description = "头像图片url")
    private String avatarUrl;

    /**
     * 1-男; 2-女
     */
    @Schema(description = "1-男; 2-女")
    private Integer gender;

    @Schema(description = "出生年月日(mills)")
    private Long birthday;

    @Schema(description = "用户角色")
    private List<RoleVo> roles;



}
