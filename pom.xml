<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.cisp</groupId>
	<artifactId>cisp-parent</artifactId>
	<version>${revision}</version>
	<packaging>pom</packaging>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <revision>1.0.0-SNAPSHOT</revision>
        <kotlin.version>1.9.21</kotlin.version>
    </properties>

    <repositories>
        <repository>
            <id>jfrog-virtual</id>
            <name>artifactory-releases</name>
            <url>https://artifactory.gz.cvte.cn/artifactory/SR_maven_virtual/</url>
            <releases>
                <updatePolicy>always</updatePolicy>
            </releases>
            <snapshots>
                <!--   always,daily,interval,never    -->
                <updatePolicy>never</updatePolicy>
            </snapshots>
        </repository>
        <repository>
            <id>nexus</id>
            <name>nexus</name>
            <url>https://mvn.gz.cvte.cn/nexus/content/groups/public/</url>
            <releases>
            </releases>
            <snapshots>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>
    <distributionManagement>
        <repository>
            <id>jfrog-releases</id>
            <name>artifactory-releases</name>
            <url>https://artifactory.gz.cvte.cn/artifactory/SR_maven_releases_local/</url>
        </repository>
        <snapshotRepository>
            <id>jfrog-snapshots</id>
            <name>artifactory-snapshots</name>
            <url>https://artifactory.gz.cvte.cn/artifactory/SR_maven_snapshots_local</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.3.0</version>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>3.3.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.2.1</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>flatten-maven-plugin</artifactId>
                    <version>1.3.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>2.4</version>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>2.2.6.RELEASE</version>
                </plugin>
                <plugin>
                    <groupId>org.mybatis.generator</groupId>
                    <artifactId>mybatis-generator-maven-plugin</artifactId>
                    <version>1.3.7</version>
                    <configuration>
                        <configurationFile>${basedir}/src/main/resources/generator/generatorConfig.xml</configurationFile>
                        <overwrite>true</overwrite>
                        <verbose>true</verbose>
                    </configuration>
                    <dependencies>
                        <dependency>
                            <groupId>mysql</groupId>
                            <artifactId>mysql-connector-java</artifactId>
                            <version>8.0.11</version>
                        </dependency>
                        <dependency>
                            <groupId>tk.mybatis</groupId>
                            <artifactId>mapper</artifactId>
                            <version>4.1.5</version>
                        </dependency>
                        <dependency>
                            <groupId>com.seewo.kishframework</groupId>
                            <artifactId>kish-mybatis</artifactId>
                            <version>2.0.0-SNAPSHOT</version>
                        </dependency>
                    </dependencies>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.cisp</groupId>
                <artifactId>cisp-bom</artifactId>
                <type>pom</type>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cisp</groupId>
                <artifactId>cisp-dependencies-api</artifactId>
                <type>pom</type>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cisp</groupId>
                <artifactId>cisp-dependencies-service</artifactId>
                <type>pom</type>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cisp</groupId>
                <artifactId>cisp-infrastructure</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cisp</groupId>
                <artifactId>cisp-common-model</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cisp</groupId>
                <artifactId>pts-agent</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cisp</groupId>
                <artifactId>api-gateway-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cisp</groupId>
                <artifactId>api-gateway</artifactId>
                <classifier>mixin</classifier>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cisp</groupId>
                <artifactId>cisp-device-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cisp</groupId>
                <artifactId>cisp-identity</artifactId>
                <classifier>mixin</classifier>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cisp</groupId>
                <artifactId>cisp-identity-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cisp</groupId>
                <artifactId>cisp-device</artifactId>
                <classifier>mixin</classifier>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cisp</groupId>
                <artifactId>cisp-access-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cisp</groupId>
                <artifactId>cisp-access</artifactId>
                <classifier>mixin</classifier>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cisp</groupId>
                <artifactId>api-gateway-sdk</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cisp</groupId>
                <artifactId>cisp-system-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cisp</groupId>
                <artifactId>cisp-system</artifactId>
                <classifier>mixin</classifier>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cisp</groupId>
                <artifactId>cisp-event-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cisp</groupId>
                <artifactId>cisp-event</artifactId>
                <classifier>mixin</classifier>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cisp</groupId>
                <artifactId>cisp-video-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cisp</groupId>
                <artifactId>cisp-video</artifactId>
                <classifier>mixin</classifier>
                <version>${revision}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <modules>
        <module>dep/cisp-bom</module>
        <module>dep/cisp-dependencies-api</module>
        <module>dep/cisp-dependencies-service</module>
        <module>infrastructure/cisp-infrastructure</module>
        <module>infrastructure/cisp-common-model</module>
        <module>application/cisp-dsa-parent</module>
        <module>mixin/cisp-mixin-all</module>
        <module>application/api-gateway-parent</module>
        <module>application/cisp-device-parent</module>
        <module>application/cisp-access-parent</module>
        <module>application/cisp-system-parent</module>
        <module>application/cisp-event-parent</module>
        <module>application/cisp-video-parent</module>
        <module>service/cisp-identity-parent</module>
        <module>mixin/cisp-mixin-all-edge</module>
    </modules>
</project>